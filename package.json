{"name": "yandu", "version": "3.8.5", "description": "演都系统管理后台", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "dayjs": "1.11.10", "echarts": "5.4.0", "element-plus": "2.3.0", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "lodash": "4.17.21", "mitt": "3.0.1", "moment": "2.30.1", "nprogress": "0.2.0", "pinia": "2.0.22", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-json-viewer": "3", "vue-router": "4.1.4", "vue3-json-viewer": "2.2.2", "vue3-text-clamp": "0.1.2"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}