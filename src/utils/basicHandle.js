
import { resetForm } from './ruoyi.js'

// 弹窗宽度规定
export function dialogSize(size){
    if(size === 'small'){
        return '600'
    }
    else if(size === 'default'){
        return '780'
    }
    else if(size === 'large'){
        return '900'
    }
    else{
      return size
    }
}
// 列表宽度规定
export function formItemSize(size){
    if(size === 'default'){
        return 24
    }
    else if(size === 'small'){
        return 12
    }
    else{
      return size
    }
}

// 通过对象数组中，对象的某一属性进行分组
export function groupBy(group, array){
  return group && typeof group==='function' && Array.isArray(array)?array.reduce(function(nVal,cVal){
    var k = group(cVal)

    // console.log(nVal, '=====', k)
    if(nVal.hasOwnProperty(k)){
      nVal[k].push(cVal)
    }
    else{
      nVal[k] = [cVal]
    }

    return nVal
  },{}):this
}

// 对数组内部的元素进行排列组合
export function arrayPermutations(arr) {
  const result = [];

  function permuteHelper(currentArr, remainingArr) {
    if (remainingArr.length === 0) {
      result.push(currentArr);
    } else {
      for (let i = 0; i < remainingArr[0].length; i++) {
        permuteHelper(
          currentArr.concat(remainingArr[0][i]),
          remainingArr.slice(1)
        );
      }
    }
  }

  permuteHelper([], arr);
  return result;
}


// 去html的标签
export function htmlToStr(html) {
  if(typeof html !== 'string') return ''
  return html.replace(/(<([^>]+)>)/ig, "")
}

export function addBaseUrl(url){
  return String(import.meta.env.VITE_APP_BASE_API) + url
}
/**
 * flexWidth
 * @param prop 每列的prop 可传''
 * @param tableData 表格数据
 * @param title 标题长内容短的，传标题  可不传
 * @param num 列中有标签等加的富余量
 * @returns 列的宽度
 * 注：prop,title有一个必传
 */
export function flexWidth(prop,tableData,title,num=0){
    //表格没有数据时不做处理
    if(tableData.length ===0){
      return;
    }
    let flexWidth = 0;//初始化表格列宽
    let columnContent = '';//占位最宽的内容
    let canvas = document.createElement("canvas");
    let context = canvas.getContext('2d');
    context.font = "14px Microsoft YaHei";
    if((prop === '') && title){
      //标题长内容少的直接取标题的宽值
      columnContent = title
    }
    else{
      //获取该列中占位最宽的部分
      let index = 0;
      for(let i =0; i<tableData.length; i++){
        const now_temp = tableData[i][prop]+'';
        const max_temp = tableData[index][prop]+'';
        const now_temp_w = context.measureText(now_temp).width;
        const max_temp_w = context.measureText(max_temp).width;
        if(now_temp_w > max_temp_w){
          index = i
        }
      }
      columnContent = tableData[index][prop]
      //比较占位最宽的值和标题，标题为空的留出四个位置
      const column_w = context.measureText(columnContent).width
      const title_w = context.measureText(title).width
      if(column_w < title_w){
        columnContent = title || '留四个字'
      }
    }
    //计算最宽内容的列宽
    let width = context.measureText(columnContent);
    flexWidth = width.width + 40 + num
    return flexWidth + 'px'
  }