import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { relevance_flag } = useDict('relevance_flag')

export const tableConfig = {
  items: [
    { label: '剧目名称', prop: 'repertoireName', type: 'String', option: {} },
    { label: '剧场名称', prop: 'theaterName', type: 'String', option: {} },
    { label: '所属商家', prop: 'merchantName', type: 'String', option: {} },
    {
      label: '表演场次',
      prop: 'repertoireInfoDetailList',
      type: 'Slot',
      option: {
        minWidth: 400,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '确认状态',
      prop: 'status',
      type: 'Dict',
      option: {
        pull: relevance_flag,
      },
    },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '所属商家',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: pull.merchant,
      },
    },
    {
      label: '确认状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: relevance_flag,
      },
    },
    { label: '演出时间', prop: 'showTime', type: 'daterange', option: {} },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
