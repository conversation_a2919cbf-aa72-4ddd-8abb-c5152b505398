<template>
  <div class="app-container">
    <SearchForm
      v-model:open="queryOpen"
      :options="queryOptions"
      v-model:query="queryParams"
      @search="getList"
    />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧目 / 剧场名称">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info"
            >高级筛选</el-button
          >
        </el-col>
      </template>
    </HandleRow>
    <CommonTable
      :listReq="$api.repertoireInfo.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols"
      v-model:multiple="multiple"
      ref="tableRef"
    >
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="100"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleLink(scope.row)"
            :disabled="scope.row.status === 2"
            >{{
              (scope.row.status === 1 ? "取消" : "确认") + `关联`
            }}</el-button
          >
        </template>
      </el-table-column>

      <template #repertoireInfoDetailList="{ data }">
        <div
          class="timeRange"
          v-for="(item, index) in data.scope.row.repertoireInfoDetailList"
          :key="index"
        >
          <span>
            {{ moment(item.startTime).format("YYYY-MM-DD dddd HH:mm") }} -
            {{ moment(item.endTime).format("YYYY-MM-DD dddd HH:mm") }}
          </span>
        </div>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >
      <CommonForm
        v-model:form="form"
        :formOptions="formOptions"
        :readonly="dialogControl.readonly"
        ref="formRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Theater">
import { nextTick } from "vue";
import { tableConfig, formConfig, queryConfig } from "./config";
import moment from "moment";
import "moment/dist/locale/zh-cn";
moment.locale("zh-cn");
const { proxy } = getCurrentInstance();

/* 查询和表格配置 */
const queryOpen = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
});
const queryOptions = ref(queryConfig.items);
const getList = () => {
  proxy.$refs.tableRef.getList();
};
const multiple = ref(true);
const tableCols = ref(tableConfig.items);

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  form: {},
  readonly: false,
});
const formOptions = ref(formConfig.items);
// 重置
function reset() {
  dialogControl.value.form = {};
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false;
  dialogControl.value.readonly = false;
  reset();
}

function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs["formRef"].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(
        proxy.$api.repertoireInfo.add,
        proxy.$api.repertoireInfo.update,
        dialogControl.value
      );
    }
  });
}

function handleLink(row) {
  if (row.status === 1) {
    proxy.$api.repertoireInfo
      .updateRelevance({
        id:row.id,
        repertoirePass: 2,
        theaterPass: 2,
        status: 2,
      })
      .then((res) => {
        proxy.$modal.msgSuccess("关联已取消");
        getList();
      });
  } else if (row.status === 0) {
    proxy.$api.repertoireInfo
      .updateRelevance({
        id:row.id,
        repertoirePass: 1,
        theaterPass: 1,
        status: 1,
      })
      .then((res) => {
        proxy.$modal.msgSuccess("关联已确认");
        getList();
      });
  }
}
</script>
