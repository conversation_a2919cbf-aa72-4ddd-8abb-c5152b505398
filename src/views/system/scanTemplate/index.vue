<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="模板名称">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.scanTemplate.list" :queryParams="queryParams" :tableCols="tableCols"
      v-model:multiple="multiple" ref="tableRef" :statusReq="$api.scanTemplate.updateStatus">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>

          <el-button link type="danger" @click="handleDel(scope.row)" v-if="scope.row.defaultFlag === 0">删除</el-button>
        </template>
      </el-table-column>

      <template #type="{ data }">
        <dict-tag v-for="(item, index) in data.scope.row.scanningInfoList" :key="index" :options="scan_field_type"
          :value="item.type" />
        <!-- <div v-for="(item, index) in data.scope.row.scanningInfoList" :key="index">{{ item.type }}</div> -->
      </template>

      <template #value="{ data }">
        <div v-for="(item, index) in data.scope.row.scanningInfoList" :key="index">{{ item.value }}</div>
      </template>
    </CommonTable>

    <!-- 详情对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
      @closed="cancel" ref="dialogRef">
      <el-form :model="dialogControl.form" labelWidth="auto" ref="formRef" class="commonForm">
        <el-form-item label="模板名称" prop="name" :rules="{ required: true, message: '模板名称不能为空' }">
          <el-input v-model="dialogControl.form.name" placeholder="请输入模板名称" clearable />
        </el-form-item>

        <div class="felxWrap">
          <div class="row">
            <el-form-item class="keyCol1" label="平台的Key" required labelWidth="auto"></el-form-item>
            <el-form-item class="keyCol2" label="识别的Key" required labelWidth="auto"></el-form-item>
            <div class="optionCol"></div>
          </div>

          <div class="row" v-for="(i, index) in dialogControl.form.scanningInfoList" :key="index">
            <el-form-item class="keyCol1" labelWidth="0" :prop="`scanningInfoList[${index}].type`"
              :rules="{ required: true, message: '平台的Key不能为空' }">
              <!-- <el-input v-model="i.type" placeholder="请输入平台的Key" clearable /> -->
              <el-select v-model="i.type" placeholder="请选择平台的Key">
                <el-option v-for="item in scan_field_type" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <el-form-item class="keyCol2" labelWidth="0" :prop="`scanningInfoList[${index}].value`"
              :rules="{ required: true, message: '识别的Key不能为空' }">
              <el-input v-model="i.value" placeholder="请输入识别的Key" clearable />
            </el-form-item>

            <div class="optionCol">
              <el-icon class="addIcon" color="#409eff" :size="24" @click="handleAddKey">
                <Plus />
              </el-icon>

              <el-icon class="minusIcon" color="#fab6b6" :size="24" @click="handleMinusKey(index)"
                v-if="index != 0 || dialogControl.form.scanningInfoList.length > 1">
                <Minus />
              </el-icon>
            </div>
          </div>
        </div>

        <el-form-item label="启动状态">
          <el-switch v-model="dialogControl.form.status" :inactive-value="0" :active-value="1"
            :disabled="dialogControl.form.defaultFlag === 1" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Theater">
  import { formConfig, queryConfig, tableConfig } from './config'
  import _ from 'lodash'
  import { useDict } from '@/utils/dict'

  const { scan_field_type } = useDict('scan_field_type')

  const { proxy } = getCurrentInstance()

  /* 查询和表格配置 */
  const queryOpen = ref(false)
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    status: undefined,
  })
  const queryOptions = ref(queryConfig.items)
  const getList = () => {
    proxy.$refs.tableRef.getList()
  }
  const multiple = ref(false)
  const tableCols = ref(tableConfig.items)

  /* 操作和表单配置 */
  const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false,
  })

  // 重置
  function reset() {
    dialogControl.value.form = {
      name: '',
      status: 0,
      scanningInfoList: [{ type: '', value: '' }],
    }
  }

  /* 查看详情 */
  function handleEdit(row) {
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.scanTemplate.detail, row, dialogControl.value, '修改识别模板', (res) => {
      if (!res.data.scanningInfoList || !res.data.scanningInfoList.length) res.data.scanningInfoList = [{ type: '', value: '' }]
    })
  }

  /* 删除 */
  function handleDel(row) {
    proxy.$modal
      .confirm('是否确认删除所选的数据项？')
      .then(function () {
        return proxy.$api.scanTemplate.remove(row.id)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => { })
  }

  // 添加
  function handleAdd() {
    reset()
    proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加识别模板')
  }
  // 取消
  function cancel() {
    dialogControl.value.open = false
    dialogControl.value.readonly = false
    reset()
  }

  /* 添加Key */
  function handleAddKey() {
    dialogControl.value.form.scanningInfoList.push({ type: '', value: '' })
  }

  /* 删除Key */
  function handleMinusKey(index) {
    dialogControl.value.form.scanningInfoList.splice(index, 1)
  }

  // 提交
  function handleSubmit() {
    proxy.$refs.formRef.validate((valid, fields) => {
      if (valid) {
        if (dialogControl.value.form.id) {
          proxy.$api.scanTemplate.update(dialogControl.value.form).then((res) => {
            dialogControl.value.open = false
            getList()
          })
        } else {
          proxy.$api.scanTemplate.add(dialogControl.value.form).then((res) => {
            dialogControl.value.open = false
            getList()
          })
        }
      } else {
        console.log('error submit!', fields)
      }
    })
  }
</script>

<style lang="scss" scoped>
  .commonForm {
    :deep(.el-input-number) {
      width: 100%;

      .el-input__inner {
        text-align: left;
        width: 100%;
      }
    }

    .tip {
      margin-left: 4em;
      line-height: normal;
      color: rgba(153, 153, 153, 1);
    }

    .doubleImage {
      display: flex;

      .component-upload-image {
        margin-right: 10px;
      }
    }

    .positonTop {
      display: block;

      &:deep(.el-form-item__label) {
        width: fit-content !important;
      }

      &:deep(.el-form-item__content) {
        padding-left: 3em;
        padding-top: 8px;
      }
    }

    .felxWrap {
      width: 100%;

      .row {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 18px;

        .keyCol1 {
          width: 200px;
          margin-right: 48px;
          margin-bottom: 0;
        }

        .keyCol2 {
          width: 200px;
          margin-bottom: 0;
          margin-right: 18px;
        }

        .optionCol {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .addIcon,
          .minusIcon {
            cursor: pointer;
          }

          .addIcon {
            margin-right: 18px;
          }
        }
      }
    }
  }
</style>