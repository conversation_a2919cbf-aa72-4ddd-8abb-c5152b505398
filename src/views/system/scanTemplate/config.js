export const tableConfig = {
  items: [
    { label: '模板名称', prop: 'name', type: 'String', option: {} },
    { label: '平台的Key', prop: 'type', type: 'Slot', option: {} },
    { label: '识别的Key', prop: 'value', type: 'Slot', option: {} },
    { label: '启用状态', prop: 'status', type: 'Switch', option: {} },
    // { label: '是否默认', prop: 'name1', type: 'Image', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'String', option: {} },
  ],
}

export const formConfig = {
  items: [
    { label: '模板名称', prop: 'name', type: 'String', option: {}, size: 'default' },
    { label: '平台的Key', prop: 'type', type: 'Slot', option: {}, size: 'default' },
    { label: '识别的Key', prop: 'value', type: 'Slot', option: {}, size: 'default' },
    { label: '启用状态', prop: 'status', type: 'Radio', option: {}, size: 'default' },
    // { label: '是否默认', prop: 'name1', type: 'String', option: {}, size: 'default' },
  ],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '启用状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: [
          { value: 1, label: '是' },
          { value: 0, label: '否' },
        ],
      },
    },
  ],
}
