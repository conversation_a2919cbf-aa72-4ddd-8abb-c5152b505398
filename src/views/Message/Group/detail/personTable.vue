<template>
    <!-- <CommonTable :data="list" :queryParams="queryParams" :tableCols="tableCols" ref="tableRef" /> -->
  
    <el-table :data="list" flexible>
      <el-table-column label="序号" align="center" type="index" width="55" />
      <el-table-column label="用户头像" prop="image" align="center">
        <template #default="scope">
          <DefaultAvatar :url="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column label="用户名称" prop="name" align="center" />
      <el-table-column label="用户手机号" prop="phone" align="center" />
      <el-table-column label="等级勋章" prop="medal" align="center">
        <template #default="scope">
          <div style="display: flex;justify-content: center;">
              <LevelMedal
                    :color="scope.row.rankMedalColor"
                    :name="scope.row.rankMedalName"
                    :level="scope.row.rankMedalLevel"
                />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </template>
  
  <script setup name="personTable">
  const { list } = defineProps({
    list: {
      type: Array,
      default: [],
    },
  });
  const tableCols = ref([
    // { label: '编号', prop: 'i', type: 'String', option: {} },
    { label: "用户头像", prop: "image", type: "Avatar", option: {} },
    { label: "用户名称", prop: "name", type: "String", option: {} },
    { label: "用户手机号", prop: "phone", type: "String", option: {} },
    { label: "等级勋章", prop: "medal", type: "String", option: {} },
  ]);
  </script>
  