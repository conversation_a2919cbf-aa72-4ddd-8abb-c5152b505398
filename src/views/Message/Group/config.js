import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull

export const tableConfig = {
  items: [
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    {
      label: '群发内容',
      prop: 'body',
      type: 'String',
      option: {
        minWidth: 300,
        showTip: true,
      },
    },
    { label: '发送时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '同步发送短信',
      prop: 'smsNotify',
      type: 'Select',
      option: {
        pull: [
          { value: 0, label: '否' },
          { value: 1, label: '是' },
        ],
      },
    },
    // { label: '发送状态', prop: 'status', type: 'Dict', option: {
    //     pull:[]
    // } },
  ],
}

export const formConfig = {
  items: [],
  size: 'large',
}

export const queryConfig = {
  items: [
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
