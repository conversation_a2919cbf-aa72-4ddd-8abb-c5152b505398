<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" :needPart="true" />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="群发内容" :needPart="true">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>
    <CommonTable
      :listReq="$api.message.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols"
      :needPart="true"
      v-model:multiple="multiple"
      ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="warning" @click="handleView(scope.row)">详情</el-button>
        </template>
      </el-table-column>
      <template #content="scope">
        {{ 'content' }}
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <el-form :model="dialogControl.form" ref="formRef" label-width="8em" class="groupForm">
        <el-form-item :label="'关联' + (queryParams.merchantCategory === '1' ? '剧目' : '剧场')" size="normal">
          <span>{{ dialogControl.form.repertoireName || dialogControl.form.theaterName }}</span>
        </el-form-item>
        <el-form-item label="群发内容" size="normal">
          <span style="word-break: break-all">{{ dialogControl.form.body }}</span>
        </el-form-item>
        <el-form-item label="发送时间" size="normal">
          <span>{{ dialogControl.form.createTime }}</span>
        </el-form-item>
        <el-form-item label="同步发送短信" size="normal">
          <span v-if="dialogControl.form.smsNotify == 1">是</span>
          <span v-if="dialogControl.form.smsNotify == 0">否</span>
        </el-form-item>
        <el-form-item label="发送对象" size="normal" class="personsItem">
          <PersonTable :list="dialogControl.form.userList" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup name="Group">
import { nextTick } from 'vue'
import { tableConfig, formConfig, queryConfig } from './config'
import PersonTable from './detail/personTable.vue'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  merchantCategory: '1',
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: '0',
  }
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加商家')
}
function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.message.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.message.detail, row, dialogControl.value, '修改商家')
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.message.remove, row)
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs['formRef'].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(proxy.$api.message.add, proxy.$api.message.update, dialogControl.value)
    }
  })
}
</script>
<style lang="scss" scoped>
.groupForm {
  .el-form-item {
    margin-right: 20px;
    .importBtn {
      margin-left: 16px;
    }

    &.rowItem {
      width: 100%;
      font-size: 16px;
    }
    &.personsItem {
      flex-direction: column;
      :deep(.el-form-item__content) {
        padding: 10px 20px 0;
        display: block;
      }
    }
  }
}
</style>
