import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
export const tableConfig = {
  items: [
    // { label: '编号', prop: 'id', type: 'Integer', option: {} },
    {
      label: '用户头像',
      prop: 'userAvatar',
      type: 'Avatar',
      option: {
        minWidth: 120,
      },
    },
    { label: '用户昵称', prop: 'userName', type: 'String', option: {} },
    { label: '手机号码', prop: 'phone', type: 'Integer', option: {} },
    {
      label: '佩戴勋章',
      prop: 'medal',
      type: 'Slot',
      option: {
        minWidth: 200,
      },
    },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    { label: '关注时间', prop: 'treasureTime', type: 'Time', option: {} },
    { label: '最近回复时间', prop: 'userLastTime', type: 'Time', option: {} },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '关注时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
