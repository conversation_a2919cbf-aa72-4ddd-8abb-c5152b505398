<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户名称 / 剧目 / 剧场">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.userMessage.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="100" fixed="right">
        <template #default="scope">
          <el-button link type="warning" @click="handleView(scope.row)">详情</el-button>
        </template>
      </el-table-column>

      <template #medal="{ data }">
        <LevelMedal
          :color="data.scope.row.rankMedalColor || '#009944'"
          :name="data.scope.row.rankMedalName"
          :level="data.scope.row.rankMedalLevel"
          style="margin: 0 auto" />
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap chat"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <div class="chatBox">
        <div class="playInfo">
          <PlayInfoMini
            :cover="dialogControl.form.repertoireCoverPicture || dialogControl.form.theaterCoverPicture"
            :name="dialogControl.form.repertoireName || dialogControl.form.theaterName"
            :subTime="dialogControl.form.treasureTime"></PlayInfoMini>
        </div>
        <div v-for="talk in dialogControl.form.userMessageInfoList" :key="talk.id">
          <div class="talk rightTalk" v-if="talk.userMerchantId">
            <div class="info">
              <div class="text1">
                <span class="time">{{ talk.createTime }}</span>
                <span class="name">{{ dialogControl.form.repertoireName || dialogControl.form.theaterName + '官方客服' }}</span>
              </div>
              <div class="text2">
                <div class="content">{{ talk.body }}</div>
              </div>
            </div>
            <div class="avatar">
              <DefaultAvatar :url="dialogControl.form.repertoireCoverPicture || dialogControl.form.theaterCoverPicture" :size="60" />
            </div>
          </div>
          <div class="talk leftTalk" v-else-if="talk.userId">
            <div class="avatar">
              <DefaultAvatar :url="dialogControl.form.userAvatar || talk.replyAvatar" :size="60" />
            </div>
            <div class="info">
              <div class="text1">
                <span class="name">{{ dialogControl.form.userName || 'xxx' }}</span>
                <span class="time">{{ talk.createTime }}</span>
              </div>
              <div class="text2">
                <div class="content">{{ talk.body }}</div>
              </div>
            </div>
          </div>
          <div class="talk rightTalk" v-else>
            <div class="info">
              <div class="text1">
                <span class="time">{{ talk.createTime }}</span>
                <span class="name">演都官方客服</span>
              </div>
              <div class="text2">
                <div class="content">{{ talk.body }}</div>
              </div>
            </div>
            <div class="avatar">
              <DefaultAvatar url="@/assets/logo/logo.png" :size="60" />
            </div>
          </div>
        </div>
      </div>
      <div class="replyBox">
        <el-input v-model="replyCon" type="textarea" class="replyCon" :rows="5" placeholder="请发文字进行交流"></el-input>
        <div class="btnRow">
          <el-button type="primary" @click="submitForm">发送</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Letter">
import { formConfig, queryConfig, tableConfig } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const selRow = ref('') // 选中的消息
const replyCon = ref('') // 回复内容

// 重置
function reset() {
  dialogControl.value.form = {}
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}

function handleView(row) {
  dialogControl.value.readonly = true
  selRow.value = row
  replyCon.value = ''
  console.log('🚀 ~ handleView ~ selRow.value🚀', selRow.value)
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.userMessage.detail, row, dialogControl.value, '查看详情')
}

function submitForm() {
  if (!replyCon.value) {
    proxy.$modal.alertWarning('回复内容不能为空')
    return
  }

  let data = {
    merchantId: -1,
    userMessageId: dialogControl.value.form.id,
    repertoireId: dialogControl.value.form.repertoireId || undefined,
    theaterId: dialogControl.value.form.theaterId || undefined,
    body: replyCon.value,
  }
  console.log('🚀 ~ submitForm ~ data🚀', data)

  proxy.$api.userMessageInfo.add(data).then((res) => {
    replyCon.value = ''
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.userMessage.detail, selRow.value, dialogControl.value, '查看详情')
    console.log('🚀 ~ proxy.$api.userMessageInfo.add ~ res🚀', res)
  })
}
</script>

<style lang="scss">
.chat.dialogWrap {
  .el-dialog__body {
    padding: 0 !important;
    background-color: #f4f4f4;
  }
}
</style>

<style lang="scss" scoped>
.chatBox {
  box-sizing: border-box;
  padding: 10px 20px 30px;
  width: 100%;

  .playInfo {
    position: sticky;
    top: 10px;
    right: 0;
    left: 0;
    margin: 0 auto;
    margin-bottom: 20px;
    width: 94%;
    border-radius: 10px;
    background-color: #ffffff;
  }

  .talk {
    display: flex;
    align-items: flex-start;
    margin-bottom: 42px;
    padding: 0 24px;

    .avatar {
      flex-shrink: 0;
    }

    .info {
      flex-grow: 1;

      .text1 {
        line-height: 32px;

        span {
          margin-right: 40px;
          font-size: 14px;

          &:last-child {
            margin-right: 0;
          }
        }

        .name {
          font-weight: 600;
        }
      }

      .text2 {
        display: inline-block;
        padding: 10px;
        border-radius: 10px;
        background-color: #ffffff;

        .content {
          word-break: break-all;
          line-height: 22px;
        }
      }
    }

    &.leftTalk {
      .avatar {
        margin-right: 18px;
      }
    }

    &.rightTalk {
      .avatar {
        margin-left: 18px;
      }

      .info {
        text-align: right;
      }
    }
  }
}

.replyBox {
  box-sizing: border-box;
  padding: 20px;
  width: 100%;
  background: #ffffff;

  .replyCon {
    overflow: hidden;
    border-radius: 8px;
    background: #f4f4f4;

    &:deep(.el-textarea__inner) {
      background: #f4f4f4;
      box-shadow: none;
      resize: none;
    }
  }

  .btnRow {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 10px;
    width: 100%;
  }
}
</style>
