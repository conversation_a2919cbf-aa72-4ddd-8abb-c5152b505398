import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull

export const tableConfig = {
  items: [
    // { label: '编号', prop: 'id', type: 'String', option: {} },
    {
      label: '自动回复语',
      prop: 'body',
      type: 'String',
      option: {
        minWidth: 300,
        showTip: true,
        align: 'left',
      },
    },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'String', option: {} },
  ],
}

export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
