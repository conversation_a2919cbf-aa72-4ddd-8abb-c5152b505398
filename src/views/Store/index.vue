<template>
  <div class="app-container">
    <SearchForm
      v-model:open="queryOpen"
      :options="queryOptions"
      v-model:query="queryParams"
      @search="getList"
    />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="商家名称">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info"
            >高级筛选</el-button
          >
        </el-col>
      </template>
    </HandleRow>
    <CommonTable
      :listReq="$api.merchant.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols"
      v-model:multiple="multiple"
      ref="tableRef"
      @hasAdd="userStore.getPull"
      @hasUpdate="userStore.getPull"
      @hasDelete="userStore.getPull"

    >
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="120"
        fixed="right"
      >
        <template #default="scope">
          <el-button link type="warning" @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <!-- <el-button link type="primary" @click="handleView(scope.row)">详情</el-button> -->
          <el-button link type="danger" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >
      <CommonForm
        v-model:form="dialogControl.form"
        :formOptions="formOptions"
        :readonly="dialogControl.readonly"
        ref="formRef"
      >
    
      <template #merchantCategory="{ data }">
        <el-radio-group
            v-model="dialogControl.form.merchantCategory"
            :disabled="isUpdating"
          >
            <el-radio
              v-for="p in data.config.option.pull"
              :key="p.id || p.value"
              :label="Number(p.id || p.value)"
            >
              {{ p.name || p.label }}
            </el-radio>
          </el-radio-group>
      </template>
    </CommonForm>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>

     
    </el-dialog>
  </div>
</template>

<script setup name="merchant">
import { nextTick } from "vue";
import { tableConfig, formConfig, queryConfig } from "./config";
import useUserStore from "@/store/modules/user";
const { proxy } = getCurrentInstance();
const userStore = useUserStore()
/* 查询和表格配置 */
const queryOpen = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
});
const queryOptions = ref(queryConfig.items);
const getList = () => {
  proxy.$refs.tableRef.getList();
};
const multiple = ref(true);
const tableCols = ref(tableConfig.items);

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  form: {},
  readonly: false,
});
const formOptions = ref(formConfig.items);
const isUpdating = ref(false)
// 重置
function reset() {
  dialogControl.value.form = {
    merchantCategory: 2
  };
  isUpdating.value = false
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false;
  dialogControl.value.readonly = false;
  reset();
}
function handleAdd() {
  reset();
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, "添加商家");
}
function handleView(row) {
  dialogControl.value.readonly = true;
  proxy.$refs.tableRef.handleGetDetail(
    proxy.$api.merchant.detail,
    row,
    dialogControl.value,
    "查看详情"
  );
}
function handleUpdate(row) {
  isUpdating.value = true
  proxy.$refs.tableRef.handleGetDetail(
    proxy.$api.merchant.detail,
    row,
    dialogControl.value,
    "修改商家"
  );
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.merchant.remove, row);
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs["formRef"].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(
        proxy.$api.merchant.add,
        proxy.$api.merchant.update,
        dialogControl.value
      );
    }
  });
}
</script>
