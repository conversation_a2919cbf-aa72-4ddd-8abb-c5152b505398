import api from '@/api/api.js'
import { useDict } from '@/utils/dict'
const { merchant_category_flag } = useDict('merchant_category_flag')

export const tableConfig = {
  items: [
    { label: '商家名称', prop: 'merchantName', type: 'String', option: {} },
    {
      label: '商家类别',
      prop: 'merchantCategory',
      type: 'Dict',
      option: {
        pull: merchant_category_flag,
      },
    },
    { label: '联系人', prop: 'contactPerson', type: 'String', option: {} },
    { label: '联系电话', prop: 'phone', type: 'String', option: {} },
    { label: '联系地址', prop: 'address', type: 'String', option: {} },
    {
      label: '备注',
      prop: 'remark',
      type: 'String',
      option: {
        minWidth: 300,
        showTip: true,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'String', option: {} },
  ],
}

export const formConfig = {
  items: [
    {
      label: '商家logo',
      prop: 'logo',
      type: 'Image',
      option: {
        limit: 1,
      },
      size: 'default',
    },
    {
      label: '商家名称',
      prop: 'merchantName',
      type: 'String',
      option: {},
      rules: [{ required: true, message: '请输入商家名称', trigger: '' }],
      size: 'default',
    },
    {
      label: '商家类别',
      prop: 'merchantCategory',
      type: 'Slot',
      option: {
        pull: merchant_category_flag,
      },
      size: 'default',
    },
    { label: '联系人', prop: 'contactPerson', type: 'String', option: {}, rules: [{ required: true, message: '请输入联系人', trigger: '' }], size: 'default' },
    { label: '联系电话', prop: 'phone', type: 'String', option: {}, rules: [{ required: true, message: '请输入联系电话', trigger: '' }], size: 'default' },
    { label: '联系地址', prop: 'address', type: 'String', option: {}, size: 'default' },
    // { label: '合作开始时间', prop: 'startTime', type: 'String', option: {} },
    // { label: '合作结束时间', prop: 'endTime', type: 'String', option: {} },
    {
      label: '合作日期',
      prop: 'time',
      type: 'DateRange',
      option: {
        vals: ['startTime', 'endTime'],
      },
      size: 'default',
    },
    {
      label: '登录账号',
      prop: 'account',
      type: 'String',
      option: {},
      rules: [
        { required: true, message: '请输入登录账号', trigger: '' },
        { pattern: /^[a-zA-Z0-9]{1,11}$/, message: '请输入11位以内的英文字母或数字' },
      ],
      size: 'default',
    },
    {
      label: '登录密码',
      prop: 'password',
      type: 'Password',
      rules: [{ required: true, message: '请输入登录密码', trigger: '' }],
      option: {
        req: api.merchant.updatePwd,
      },
      size: 'default',
    },
    {
      label: '营业执照',
      prop: 'businessLicense',
      type: 'DoubleImage',
      option: {
        vals: ['businessLicenseFront', 'businessLicenseReverse'],
      },
      size: 'default',
    },
    // { label: '营业执照正面', prop: 'businessLicenseFront', type: 'String', option: {} },
    // { label: '营业执照反面', prop: 'businessLicenseReverse', type: 'String', option: {} },
    {
      label: '备注',
      prop: 'remark',
      type: 'String',
      option: {
        type: 'textarea',
        rows: '4',
      },
      size: 'default',
    },
  ],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '商家类别',
      prop: 'merchantCategory',
      type: 'tags',
      option: {
        pull: merchant_category_flag,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
