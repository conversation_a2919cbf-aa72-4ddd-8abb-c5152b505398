<template>
    <div class="app-container">
        <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
        <HandleRow v-model:query="queryParams" @search="getList" placeholder="等级勋章名称" v-if="!props.disableSearch">
            <template #btns>
                <el-col :span="1.5">
                    <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
                </el-col>
            </template>
        </HandleRow>
        <CommonTable :listReq="$api.rankMedal.listByPage" :queryParams="queryParams" :tableCols="tableCols"
            v-model:multiple="multiple" ref="tableRef">
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
                <template #default="scope">
                    <!-- <el-button link type="primary" @click="handleCheck(scope.row)">审核</el-button> -->
                    <BtnForCheck :req="$api.rankMedal.audit" :disabled="scope.row.audit" :rowId="scope.row.id" @change="getList"/>
                    <el-button link type="warning" @click="handleView(scope.row)">详情</el-button>
                    <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>

            <template #merchantName="{data}">
                {{ data.scope.row.theaterName || data.scope.row.repertoireName }}
            </template>
        </CommonTable>

        <!-- 添加或修改公告对话框 -->
        <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
        @closed="cancel"
            ref="dialogRef">

            <el-form :model="dialogControl.form" label-width="5em" :inline="false" size="normal" class="commonForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="勋章名称">
                            {{ dialogControl.form.name }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="勋章性质">
                            {{ dialogControl.form.merchantCategory === 1?'剧目':'剧场' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="'关联' + (dialogControl.form.merchantCategory === 1?'剧目':'剧场')">
                            {{ dialogControl.form.repertoireName || dialogControl.form.theaterName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="创建时间">
                            {{ dialogControl.form.createTime }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <ConditionList v-model:form="dialogControl.form.rankMedalInfoList" readonly/>
                    </el-col>
                
                </el-row>
                
            </el-form>
            
            <!-- <CommonForm v-model:form = "dialogControl.form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef">
                <template #rankMedalInfoList='scope'>
                    <ConditionList v-model:form="dialogControl.form.rankMedalInfoList" readonly/>
                </template>
            </CommonForm> -->
            <template #footer>
                <div class="dialog-footer" v-if="!dialogControl.readonly">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="cancel">取 消</el-button> -->
                </div>
            </template>
        </el-dialog>
    </div>
</template>
 
<script setup name="Medal">
import { nextTick } from 'vue';
import ConditionList from './detail/conditionList.vue';

import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance();
const props = defineProps({
  theaterId:{
    type:String,
    default:undefined
  },
  repertoireId:{
    type:String,
    default:undefined
  },
  disableSearch:{
    type:Boolean,
    default:false
  }
})

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    repertoireId: props.repertoireId || undefined,
    theaterId: props.theaterId || undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => { proxy.$refs.tableRef.getList() }
const multiple = ref(true)
const tableCols = ref(tableConfig.items)


/* 操作和表单配置 */
const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
    dialogControl.value.form = {
    };
    // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
    dialogControl.value.open = false;
    dialogControl.value.readonly = false
    reset();
}
function handleAdd() {
    reset()
    proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加商家')
}
function handleView(row) {
    dialogControl.value.readonly = true
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.rankMedal.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.rankMedal.detail, row, dialogControl.value, '修改商家')
}
function handleDelete(row) {
    proxy.$refs.tableRef.handleDelete(proxy.$api.rankMedal.remove, row)
}
function submitForm() {
    // console.log(proxy.$refs["formRef"].formRef.validate)
    proxy.$refs["formRef"].formRef.validate(valid => {
        if (valid) {
            proxy.$refs.tableRef.handleSubmit(proxy.$api.rankMedal.add, proxy.$api.rankMedal.update, dialogControl.value)
        }
    });
}






</script>

 