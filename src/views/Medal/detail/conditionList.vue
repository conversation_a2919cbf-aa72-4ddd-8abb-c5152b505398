<template>
  <div class="conditionListBox">
    <el-button type="primary" plain size="small" @click="addItem" v-if="!props.readonly"
      >添加等级</el-button
    >
    <div class="conditionList" v-if="!props.readonly">
      <el-row
        :gutter="20"
        class="conditionItem"
        v-for="(item, index) in list"
        :key="index"
      >
        <el-col :span="2">
          <span>{{ item.name }}</span>
        </el-col>
        <el-col :span="8">
          <span class="label">消费金额高于</span>
          <el-input-number
            v-model="item.expensePrice"
            placeholder="消费金额"
            :controls="false"
            @change=""
          ></el-input-number>
        </el-col>
        <el-col :span="8">
          <span class="label">消费次数高于</span>
          <el-input-number
            v-model="item.expenseNumber"
            placeholder="消费次数"
            :controls="false"
            @change=""
          ></el-input-number>
        </el-col>
        <el-col :span="4">
          <span class="label">设置颜色</span>
          <el-color-picker v-model="item.color"></el-color-picker>
        </el-col>

        <el-col :span="2">
          <el-icon class="iconBtn" title="删除" @click="removeItem(index)">
            <Close class="icon" />
          </el-icon>
        </el-col>
      </el-row>
    </div>
    <div class="conditionTable" v-else>
      <el-table :data="tableData" stripe>
        <el-table-column prop="name" label="勋章等级" align="center"> </el-table-column>
                <el-table-column prop="img" label="勋章图片" align="center">
                    <template #default='scope'>
                        <LevelMedal :name="scope.row.rankMedalName" :color="scope.row.color" :level="scope.row.name"/>
                    </template>
                </el-table-column>
                <el-table-column prop="expensePrice" label="消费金额" align="center">
                  <template #default="scope">
                    <div>
                      <svg-icon
                        class-name="yuan"
                        icon-class="yuan"
                        v-if="scope.row.expensePrice"
                      />
                      <span style="margin-left: 4px">{{ scope.row.expensePrice}}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="expenseNumber" label="消费次数" align="center"> </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup name="conditionList">
import { getCurrentInstance } from 'vue';


const { proxy } = getCurrentInstance()

const emits = defineEmits()
const props = defineProps({
    form:{
        type:Object,
        default:() => []
    },
    readonly:{
        type:Boolean,
        default:false
    }
})
const list = ref([
    // { name:"LV1", expensePrice:undefined, expenseNumber:undefined, color:'#409EFF'},
])

const tableData = computed(()=>{
    console.log(props.form)
    return props.form
})
onMounted(() => {
    list.value = props.form
})
watch(()=>list.value, val=> {
    if(val.length){
        const form = [...list.value]
        emits('update:form',form)
    }
    else{
        emits('update:form',[])
    }
},{deep:true})
const listSort = () => {
    if(list.value.length){
        list.value.forEach((i, index) => {
            i.name = `LV${index + 1}`
        })
    }
}
const addItem = ()=> {
    const length = list.value.length
    list.value.push(
        {
            name:`LV${length+1}`,
            expensePrice:undefined,
            expenseNumber:undefined,
            color:'#409EFF'
        }
    )

}

const removeItem = (index)=> {
    list.value.splice(index, 1)
    listSort()
}
</script>

<style lang="scss">
.conditionListBox {
    width: 100%;
  .conditionList {
    margin-top: 10px;
    .conditionItem {
      margin-bottom: 10px;
    }
    .el-col {
      display: flex;
      align-items: center;
      .label {
        flex-shrink: 0;
        margin-right: 10px;
      }
      .el-input-number {
        flex-shrink: 1;
      }
      .iconBtn {
        width: 100%;
        text-align: center;
        .icon {
          font-size: 18px;
          color: #909399;
          cursor: pointer;
          &:hover {
            font-size: 22px;
            transform: rotate(-90deg);
            transition: all 500ms;
          }
        }
      }
    }
  }
}
</style>
