import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { audit_flag, merchant_category_flag } = useDict('audit_flag', 'merchant_category_flag')

export const tableConfig = {
  items: [
    { label: '等级勋章名称', prop: 'name', type: 'String', option: {} },
    {
      label: '性质',
      prop: 'merchantCategory',
      type: 'Dict',
      option: {
        pull: merchant_category_flag,
      },
    },
    { label: '关联剧场/剧目', prop: 'merchantName', type: 'Slot', option: {} },
    { label: '勋章等级', prop: 'rankMedalLevel', type: 'String', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '领取数量',
      prop: 'receivedNumber',
      type: 'receivedNumber',
      option: {
        type: 4,
      },
    },
    {
      label: '状态',
      prop: 'audit',
      type: 'Audit',
      option: {
        pull: audit_flag,
        tip: 'reasonsRejection',
      },
    },
  ],
}
export const formConfig = {
  items: [
    { label: '勋章名称', prop: 'name', type: 'String', option: {}, size: 'small' },
    {
      label: '勋章性质',
      prop: 'merchantCategory',
      type: 'Read',
      option: {
        vals: '剧目',
      },
      size: 'small',
    },
    {
      label: '关联剧目',
      prop: 'repertoireId',
      type: 'Select',
      option: {
        pull: pull.repertoire,
      },
      size: 'small',
    },

    { label: '创建时间', prop: 'createTime', type: 'String', option: {}, size: 'small' },
    { label: '等级条件', prop: 'rankMedalInfoList', type: 'Slot', option: {}, size: 'default' },
    // { label:'审核', prop:'status', type:'Radio', option:{
    //     pull:audit_flag.value
    // }, size:'default' },
  ],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '商家名称',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: pull.merchant,
      },
    },
    {
      label: '勋章性质',
      prop: 'merchantCategory',
      type: 'tags',
      option: {
        pull: merchant_category_flag,
      },
    },
    {
      label: '关联剧场',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '关联剧目',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'tags',
      option: {
        pull: audit_flag,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
