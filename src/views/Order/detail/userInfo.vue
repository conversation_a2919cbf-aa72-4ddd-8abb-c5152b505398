<template>
    <div class="userInfo">
        <div class="text1">
            <div class="medal" v-if="props.rankMedalLevel">
                <LevelMedal :color="props.color || '#009944'" :name="props.rankMedalName" :level='props.rankMedalLevel'/>
            </div>
            <div class="name">
                {{ props.userName }}
            </div>
        </div>
        <div class="text2">
            <div class="phone">{{ props.phone }}</div>
        </div>
    </div>
</template>


<script setup name="userInfo">

const props = defineProps({
    userName:{
        type:String
    },
    phone:{
        type:String
    },
    color:{
        type:String,
        default:'#009944'
    },
    rankMedalName:{
        type:String
    },
    rankMedalLevel:{
        type:String
    }

})


</script>

<style lang="scss" scoped>
.userInfo{
    text-align: left;
    width: 100%;
    // overflow: hidden;
    .text1{
        display: flex;
        align-items: center;
        overflow: hidden;
        justify-content: flex-start;
        flex-wrap: wrap;
        .medal{
            margin-right: 6px;
            // flex-grow: 1;
            display: flex;
            justify-content: flex-start;
            flex-shrink: 0;
        }
        .name{
            flex-shrink: 0;
            text-overflow: ellipsis;
            white-space: nowrap;
            // float: right;
        }
    }
}

</style>