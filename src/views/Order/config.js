import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
import api from '@/api/api.js'

const { badge_type_flag, pay_status } = useDict('badge_type_flag', 'pay_status')

export const tableConfig = {
  items: [
    // { label: '编号', prop: 'no', type: 'string', option: {} },
    { label: '订单编号', prop: 'orderNo', type: 'string', option: {} },
    { label: '用户信息', prop: 'userName', type: 'Slot', option: { minWidth: 180 } },

    { label: '剧目名称', prop: 'repertoireName', type: 'string', option: {} },
    { label: '剧场名称', prop: 'theaterName', type: 'string', option: {} },
    { label: '表演场次', prop: 'time', type: 'Slot', option: {} },
    { label: '票价', prop: 'price', type: 'Price', option: {} },

    { label: '订单金额', prop: 'payPrice', type: 'Price', option: {} },
    { label: '订单时间', prop: 'createTime', type: 'Time', option: {} },
    { label: '订单状态', prop: 'payStatus', type: 'Dict', option: { pull: pay_status } },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '订单时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
