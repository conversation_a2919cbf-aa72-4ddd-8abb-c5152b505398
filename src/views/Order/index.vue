<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="订单编号、用户昵称">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Download" @click="handleExport">Excel 导出</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.userOrder.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="80" fixed="right" v-if="queryParams.badgeType !== 4">
        <template #default="scope">
          <el-button link type="warning" @click="handleView(scope.row)" v-if="scope.row.badgeType !== 4">详情</el-button>
        </template>
      </el-table-column>

      <template #userName="{ data }">
        <UserInfo
          :userName="data.scope.row.userName"
          :phone="data.scope.row.phone"
          :color="data.scope.row.color"
          :rankMedalName="data.scope.row.rankMedalName"
          :rankMedalLevel="data.scope.row.rankMedalLevel" />
      </template>

      <template #time="{ data }">
        {{ dayjs(data.scope.row.time).format('YYYY-MM-DD dddd HH:mm') }}
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <el-form :model="dialogControl.form" :rules="dialogControl.rules" label-width="7em" ref="formRef" class="commonForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="剧目名称" size="normal">
              {{ dialogControl.form.repertoireName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="剧场名称" size="normal">
              {{ dialogControl.form.theaterName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单编号" size="normal">
              {{ dialogControl.form.orderNo }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单时间" size="normal">
              {{ dialogControl.form.createTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单内容" size="normal">
              {{ `电子票、数字头像升级` }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付时间" size="normal">
              {{ dialogControl.form.payTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="支付方式" size="normal">
              {{ `微信支付` }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="交易单号" size="normal">
              {{ dialogControl.form.transactionId }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="line"></div>
      <el-form :model="dialogControl.form" :rules="dialogControl.rules" label-width="7em" label-position="top" ref="formRef" class="commonForm">
        <el-row>
          <el-col :span="24">
            <el-form-item label="升级电子票票面" size="normal">
              <div class="billRow" style="padding-left: 2em">
                <div class="bill">
                  <el-image class="billImg" :src="baseUrl + dialogControl.form.coverFront" fit="cover"></el-image>
                  <div class="tag">正面</div>
                </div>
                <div class="bill">
                  <el-image class="billImg" :src="baseUrl + dialogControl.form.coverReverse" fit="cover"></el-image>
                  <div class="tag">反面</div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="升级数字头像" size="normal">
              <div class="avatarRow" style="padding-left: 2em">
                <div class="avatar">
                  <el-image class="avatarImg" :src="baseUrl + dialogControl.form.digitalAvatarImage" fit="fill"></el-image>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup name="Order">
import { formConfig, queryConfig, tableConfig } from './config'
import UserInfo from './detail/userInfo.vue'
import dayjs from 'dayjs'
// const merchant = useUserStore().merchant

const { proxy } = getCurrentInstance()
const { badge_type_flag } = proxy.useDict('badge_type_flag')
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)

const typeOptions = computed(() => {
  const typeFlag = badge_type_flag.value

  return typeFlag
})

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {}
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加商家')
}
function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.userOrder.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.userOrder.detail, row, dialogControl.value, '修改商家')
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.userOrder.remove, row)
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs['formRef'].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(proxy.$api.userOrder.add, proxy.$api.userOrder.update, dialogControl.value)
    }
  })
}

function handleExport() {
  proxy.downloadPost(
    '/userOrder/export',
    {
      ...queryParams.value,
    },
    `订单记录导出excel_${new Date().getTime()}.xlsx`
  )
}
</script>

<style></style>
