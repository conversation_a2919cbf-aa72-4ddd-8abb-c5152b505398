<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户昵称 / 手机号">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Download" @click="handleExport">Excel 导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.userReceivingRecords.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="详情" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="warning" @click="handleView(scope.row)" v-if="scope.row.badgeType !== 4">详情</el-button>
          <div v-else style="display: flex; justify-content: center">
            <LevelMedal :name="scope.row.rankMedalInfo?.rankMedalName" :level="scope.row.rankMedalInfo?.name" :color="scope.row.rankMedalInfo?.color" />
          </div>
        </template>
      </el-table-column>

      <template #userName="{ data }">
        <UserInfo
          :userName="data.scope.row.userName"
          :phone="data.scope.row.phone"
          :color="data.scope.row.rankMedalColor"
          :rankMedalName="data.scope.row.rankMedalName"
          :rankMedalLevel="data.scope.row.rankMedalLevel" />
      </template>

      <template #time="{ data }">
        {{ dayjs(data.scope.row.time).format('YYYY-MM-DD dddd HH:mm') }}
      </template>

      <template #fileUrl="{ data }">
        <el-image
          style="width: 100px; height: 100px"
          preview-teleported
          :src="baseUrl + data.scope.row.fileUrl"
          :preview-src-list="[baseUrl + data.scope.row.fileUrl]"
          z-index="10000"
          fit="cover" />
      </template>

      <template #seatNumber="{ data }">
        <div class="flexWrap">
          <span class="tableTxt">{{ data.scope.row.seatNumber }}</span>
          <el-tooltip content="修改座位号" placement="top">
            <el-icon color="#409EFF" size="18" @click="handleEditSeat(data.scope.row)"><EditPen /></el-icon>
          </el-tooltip>
        </div>
      </template>

      <template #actorInformationList="{ data }">{{ data.scope.row.actorInformationList.join('、') }}</template>
    </CommonTable>

    <el-image-viewer v-if="dialogControl.open" :urlList="imgUrls" :zIndex="5000" @close="() => (dialogControl.open = false)" />

    <!-- 修改座位号 -->
    <el-dialog
      class="dialogWrap"
      title="修改座位号"
      v-model="editController"
      :width="400"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="editController = false"
      ref="dialogRef">
      <el-form :model="editItem" ref="formRef" :inline="false" size="normal">
        <el-form-item label="座位号" :rules="{ required: true, message: '座位号不能为空', trigger: 'blur' }" prop="seatNumber">
          <el-input clearable v-model="editItem.seatNumber" placeholder="请输入座位号"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Consume">
import dayjs from 'dayjs'
import { computed } from 'vue'
import { formConfig, queryConfig, tableConfig } from './config'
import UserInfo from './detail/userInfo.vue'

const { proxy } = getCurrentInstance()
const { badge_type_flag } = proxy.useDict('badge_type_flag')
const baseUrl = ref(import.meta.env.VITE_IMAGE)

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)

const editController = ref(false) // 修改座位弹窗控制器
const editItem = reactive({
  id: '',
  seatNumber: '',
}) // 修改对象

// 重置
function reset() {
  dialogControl.value.form = {}
  // proxy.$refs["formRef"].formRef.resetFields()
}

function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.userReceivingRecords.detail, row, dialogControl.value, '查看详情')
}

const imgUrls = computed(() => {
  const img1 = baseUrl.value + dialogControl.value.form?.image
  const img2 = baseUrl.value + dialogControl.value.form?.upgradeImage
  const img3 = baseUrl.value + dialogControl.value.form?.coverReverse
  let urls = []
  if (dialogControl.value.form?.upgradeImage && dialogControl.value.form.upgradeStatus !== 0) urls.push(img2)
  if (dialogControl.value.form?.coverReverse && dialogControl.value.form.upgradeStatus !== 0) urls.push(img3)
  if (dialogControl.value.form?.image) urls.push(img1)
  console.log(urls)
  return urls
})

// 获取名称
function getName(row) {
  const type = row.badgeType
  // console.log(badge_type_flag.value);
  const afterFilter = badge_type_flag.value.filter((i) => i.value == type)
  if (afterFilter.length === 1) {
    return (row.repertoireName || row.theaterName) + afterFilter[0].label
  } else {
    return ''
  }
}

function handleExport() {
  proxy.downloadPost(
    '/userReceivingRecords/export',
    {
      ...queryParams.value,
    },
    `消费记录导出excel_${new Date().getTime()}.xlsx`
  )
}

/* 修改座位号 */
const handleEditSeat = (item) => {
  editItem.id = item.id
  editItem.seatNumber = item.seatNumber

  editController.value = true
}

const handleSubmit = () => {
  proxy.$refs.formRef.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      proxy.$api.userReceivingRecords.updateSeat(editItem).then((res) => {
        console.log('🚀 ~ proxy.$api.updateSeat ~ res🚀', res)
        getList()
        editController.value = false
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>
.flexWrap {
  display: flex;
  justify-content: center;
  align-items: center;

  .tableTxt {
    margin-right: 10px;
  }
}
</style>
