import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { badge_type_flag, upgrade_status } = useDict('badge_type_flag', 'upgrade_status')

export const tableConfig = {
  items: [
    // { label: '编号', prop: 'no', type: 'string', option: {} },
    // { label: '区块链发放序列ID', prop: 'sendId', type: 'string', option: {} },
    {
      label: '用户信息',
      prop: 'userName',
      type: 'Slot',
      option: {
        minWidth: 180,
      },
    },
    {
      label: '关联剧目',
      prop: 'repertoireName',
      type: 'String',
      option: {},
    },
    {
      label: '关联剧场',
      prop: 'theaterName',
      type: 'String',
      option: {},
    },
    {
      label: '类型',
      prop: 'badgeType',
      type: 'Dict',
      option: {
        pull: badge_type_flag,
        minWidth: 100,
      },
    },
    // { label: '关联剧场', prop: 'theaterId', type: 'String', option: {} },
    // {
    //   label: "名称",
    //   prop: "name",
    //   type: "Slot",
    //   option: {
    //     minWidth: 220,
    //   },
    // },
    {
      label: '表演场次',
      prop: 'time',
      type: 'Slot',
      option: {
        minWidth: 200,
      },
    },
    { label: '扫票图片', prop: 'fileUrl', type: 'Slot', option: { minWidth: 120 } },
    { label: '座位号', prop: 'seatNumber', type: 'Slot', option: {} },
    { label: '票价', prop: 'amount', type: 'Price', option: {} },
    { label: '用户输入', prop: 'actorInformationList', type: 'Slot', option: {} },
    {
      label: '≥满足条件(消费金额)',
      prop: 'expensePrice',
      type: 'Price',
      option: {},
    },
    {
      label: '≥满足条件(观看场次数)',
      prop: 'lookNumber',
      type: 'String',
      option: {},
    },
    { label: '领取时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '是否升级',
      prop: 'upgradeStatus',
      type: 'Dict',
      option: {
        pull: upgrade_status,
        minWidth: 100,
      },
    },
    { label: '升级时间', prop: 'upgradeTime', type: 'Time', option: {} },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '类型',
      prop: 'badgeType',
      type: 'tags',
      option: {
        pull: badge_type_flag,
      },
    },
    {
      label: '领取时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
