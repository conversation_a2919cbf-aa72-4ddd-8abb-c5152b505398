<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户信息 / 内容">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.comment.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row)" :disabled="!scope.row.deleted">删除</el-button>
          <!-- <el-button link plain color="#68BBC4" @click="handleReport(scope.row)" :disabled="scope.row.status == 0"> 举报 </el-button> -->
          <el-button link type="warning" @click="handleProhibition(scope.row)">{{ scope.row.speakStatus === 1 ? '禁言' : '解禁' }}</el-button>
        </template>
      </el-table-column>

      <template #userName="{ data }">
        <UserInfo
          :userName="data.scope.row.userName"
          :phone="data.scope.row.phone"
          :color="data.scope.row.rankMedalColor"
          :rankMedalName="data.scope.row.rankMedalName"
          :rankMedalLevel="data.scope.row.rankMedalLevel" />
      </template>

      <template #content="{ data }">
        <Content :row="data.scope.row" @view="handleView(data.scope.row)" />
      </template>

      <template #theaterContent="{ data }">
        <Content type="1" :row="data.scope.row" @view="handleView(data.scope.row)" />
      </template>

      <template #status="{ data }">
        <div v-if="data.scope.row.deleted === 0" style="color: var(--el-color-danger)">已删除</div>
        <div v-else>
          <div v-if="data.scope.row.status === 1" style="color: var(--el-color-success)">正常</div>
          <div v-else style="color: #68bbc4">已举报</div>
        </div>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <ReplyForm type="comment" :form="dialogControl.form" />
    </el-dialog>
  </div>
</template>

<script setup name="Comment">
import { nextTick } from 'vue'
import UserInfo from '../Consume/detail/userInfo.vue'
import ReplyForm from './detail/replyForm.vue'
import Content from './detail/content.vue'
import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {}
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加商家')
}
function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.comment.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.comment.detail, row, dialogControl.value, '修改商家')
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.comment.remove, row)
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs['formRef'].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(proxy.$api.comment.add, proxy.$api.comment.update, dialogControl.value)
    }
  })
}

function handleReport(row) {
  proxy.$modal
    .confirm('您确认举报这条评论吗？')
    .then((res) => {
      proxy.$api.comment.report(row.id).then((res) => {
        proxy.$modal.msgSuccess('举报成功')
        getList()
      })
    })
    .catch((err) => {})
}

function handleProhibition(row) {
  proxy.$modal
    .confirm('您确认' + (row.speakStatus === 1 ? '禁言' : '解禁') + row.userName + '吗？')
    .then((res) => {
      proxy.$api.user.updateSpeakStatus(row.userId).then((res) => {
        proxy.$modal.msgSuccess((row.speakStatus === 1 ? '禁言' : '解禁') + '成功')
        getList()
      })
    })
    .catch((err) => {})
}
</script>
