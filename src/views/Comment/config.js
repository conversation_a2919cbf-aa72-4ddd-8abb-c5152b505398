import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { reply_status, top_status } = useDict('reply_status', 'top_status')

export const tableConfig = {
  items: [
    { label: '用户信息', prop: 'userName', type: 'Slot', option: { minWidth: 120 } },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    { label: '剧目评论内容', prop: 'content', type: 'Slot', option: { minWidth: 220 } },
    { label: '剧场评论内容', prop: 'theaterContent', type: 'Slot', option: { minWidth: 220 } },

    { label: '评论时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '状态',
      prop: 'status',
      type: 'Slot',
      option: {
        // pull: reply_status,
      },
    },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: reply_status,
      },
    },
    {
      label: '置顶与否',
      prop: 'isTop',
      type: 'tags',
      option: {
        pull: top_status,
      },
    },
    {
      label: '评论时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
