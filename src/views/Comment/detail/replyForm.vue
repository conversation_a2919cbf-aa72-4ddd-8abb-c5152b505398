<template>
  <div class="replyFormBox">
    <div class="playInfo">
      <PlayInfoMini
        :cover="form.repertoireCoverPicture"
        :name="form.repertoireName"
        :theaterName="form.theaterName"
        :session="form.startTime + '-' + form.endTime"
        v-if="props.type === 'comment'"
      />
      <PlayInfoMini
        :cover="form.repertoireCoverPicture || form.theaterCoverPicture"
        :name="form.repertoireName || form.theaterName"
        v-if="props.type === 'answer'"
      />
    </div>

    <div class="message1">
      <div class="commentInfo">
        <div class="text1">
          <div class="user">
            <div class="avatar">
              <DefaultAvatar :url="form.userAvatar" :size="24" />
            </div>
            <div class="name">
              {{ form.userName }}
            </div>
          </div>
        </div>
        <div class="text2" v-if="props.type === 'comment'">
          <div style="margin-bottom: 10px;">@ {{ form.repertoireName }}：{{ form.content }}</div>
          <div>@ {{ form.theaterName }}：{{ form.theaterContent }}</div>
        </div>
        <div class="text2" v-if="props.type === 'answer'">
          {{ form.content }}
        </div>
        <div class="footer">
          <div class="time">{{ moment(form.createTime).format('YYYY-MM-DD HH:mm') }}</div>
          <div class="marks" v-if="props.type === 'comment'">
            <div class="iconNum">
              <svg-icon class-name="icon" icon-class="comment" />
              <span class="num">{{ form.replyCount }}</span>
            </div>
            <div class="iconNum">
              <svg-icon class-name="icon" icon-class="like" />
              <span class="num">{{ form.likeCount }}</span>
            </div>
            <div class="iconNum">
              <svg-icon
                class-name="icon"
                icon-class="dislike"
                style="font-size: 28px; margin-right: 0"
              />
              <span class="num">{{ form.dislikeCount }}</span>
            </div>
          </div>
        </div>
      </div>
   
    </div>

    <el-divider content-position="left">
      <p class="title">
        {{
          (form.replyCount || form.userList.length) + "条" +
            (props.type === "answer" ? "问答" : "评论")
        }}
      </p>
    </el-divider>
    <div class="message2">
      <div class="commentList">
        <div
          class="commentItem"
          v-if="
            props.type === 'comment' && form.commentInfoResponseList?.length
          "
        >
          <div
            class="commentInfo"
            v-for="item in form.commentInfoResponseList"
            :key="item.id"
          >
            <div class="text1">
              <div class="user">
                <div class="avatar">
                  <DefaultAvatar v-if="item.userMerchantId > 0" :url="item.repertoireReplyStatus >0?form.repertoireCoverPicture:form.theaterCoverPicture" :size="24" />
                  <DefaultAvatar v-else :url="item.replyAvatar" :size="24" />
                </div>
                <div class="name">
                  <span v-if="item.userMerchantId > 0">
                    <span v-if="item.repertoireReplyStatus >0">{{ form.repertoireName }}官方</span>
                    <span v-if="item.theaterReplyStatus >0">{{ form.theaterName }}官方</span>
                  </span>
                  <span v-else>{{ item.replyName }}</span>
                </div>
              </div>
              <div v-if="item.userName">
                <div class="separator">@</div>
                <div class="user">
                  <!-- <div class="avatar">
                    <DefaultAvatar :url="item.userAvatar" :size="24" />
                  </div> -->
                  <div class="name" style="color: #909399;">
                    <span v-if="item.userMerchantId == '-1'">{{ form.repertoireName }}官方</span>
                    <span v-else-if="item.userMerchantId == '-2'">{{ form.theaterName }}官方</span>
                    <span v-else>{{ item.userName }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="text2">
              {{ item.content }}
            </div>
            <div class="footer">
              <div class="time">{{ moment(item.createTime).format('YYYY-MM-DD HH:mm') }}</div>
              <div class="marks" v-if="props.type === 'comment'">
                <div class="iconNum">
                  <svg-icon class-name="icon" icon-class="like" />
                  <span class="num">{{ item.likeCount }}</span>
                </div>
                <div class="iconNum">
                  <svg-icon
                    class-name="icon"
                    icon-class="dislike"
                    style="font-size: 28px; margin-right: 0"
                  />
                  <span class="num">{{ item.dislikeCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="commentItem"
          v-if="props.type === 'answer' && form.userList?.length"
        >
          <div class="commentInfo" v-for="item in form.userList" :key="item.id">
            <div class="text1">
              <div class="user">
                <div class="avatar">
                  <DefaultAvatar :url="item.replyAvatar" :size="24" />
                </div>
                <div class="name">
                  <!-- {{ item.merchantReplyName || item.replyName }} -->
                  <span v-if="item.merchantReplyName">
                    {{ (form.repertoireName || form.theaterName) + '官方客服' }}
                  </span>
                  <span v-else>
                    {{ item.replyName }}
                  </span>
                </div>
              </div>
              <!-- <div class="separator">@</div>
              <div class="user">
                <div class="avatar">
                  <DefaultAvatar :url="form.userAvatar" :size="24" />
                </div>
                <div class="name">
                  {{ form.name }}
                </div>
              </div> -->
            </div>
            <div class="text2">
              {{ item.content }}
            </div>
            <div class="footer">
              <div class="time">{{ moment(item.createTime).format('YYYY-MM-DD HH:mm') }}</div>
              <div class="marks" v-if="props.type === 'comment'">
                <div class="iconNum">
                  <svg-icon class-name="icon" icon-class="like" />
                  <span class="num">{{ item.likeCount }}</span>
                </div>
                <div class="iconNum">
                  <svg-icon
                    class-name="icon"
                    icon-class="dislike"
                    style="font-size: 28px; margin-right: 0"
                  />
                  <span class="num">{{ item.dislikeCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="replyForm">
import { computed, getCurrentInstance, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import moment from 'moment'
const { proxy } = getCurrentInstance();

const emit = defineEmits();
const props = defineProps({
  readonly: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: "comment",
  },
  form: {
    type: Object,
  },
});

onMounted(() => {
  console.log("form", props.form, useUserStore());
});
const form = computed(() => {
  return props.form;
});

const replyText = ref("");
</script>

<style lang="scss" scoped>
.replyFormBox {
  .commentInfo {
    margin-bottom: 26px;

    .text1 {
      display: flex;
      margin-bottom: 8px;
      align-items: center;
      .user {
        display: flex;
        align-items: center;
        .avatar {
          margin-right: 8px;
        }
      }
      .separator {
        margin: 0 10px;
        // font-weight: 600;
        color: #c7c7c7;
      }
    }
    .text2 {
      line-height: 20px;
    }
    .footer {
      display: flex;
      align-items: center;
      margin-top: 10px;
      .time {
        flex-grow: 1;
      }
      .marks {
        display: flex;
        align-items: center;
        .iconNum {
          display: flex;
          align-items: center;
          margin-right: 10px;
          .icon {
            font-size: 20px;
            margin-right: 4px;
          }
          .num {
            font-size: 14px;
            line-height: 20px;
            min-width: 1.5em;
          }
          .event {
            font-size: 14px;
            line-height: 20px;
            // margin-left: 4px;
            cursor: pointer;
          }
        }
      }
    }
  }
  .commentItem {
    padding-left: 20px;
  }
  .title {
    color: #606266;
  }
  .message1 {
    margin-top: 10px;
  }
  .message2 {
    .textForm {
      .formFooter {
        margin-top: 10px;
        text-align: right;
      }
    }
  }
}
</style>
