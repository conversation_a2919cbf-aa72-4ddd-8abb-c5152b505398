<template>
  <div class="marksBox">
    <div class="iconNum">
      <svg-icon class-name="icon" icon-class="comment" />
      <el-button class="num" text size="default" style="padding: 0;" @click="view">
        (
            <span style="color: #6899c4;">
                {{ form.replyCount }}
            </span>
        )
    </el-button>
      
    </div>
    <div class="iconNum">
      <svg-icon class-name="icon" icon-class="like" />
      <span class="num">{{ form.likeCount }}</span>
    </div>
    <div class="iconNum">
      <svg-icon
        class-name="icon"
        icon-class="dislike"
        style="font-size: 28px; margin-right: 0"
      />
      <span class="num">{{ form.dislikeCount }}</span>
    </div>
  </div>
</template>

<script setup name="markList">
const emit = defineEmits()
const props = defineProps({
  marks: {
    type: Array,
  },
});
const form = computed(() => {
  if (props.marks && Array.isArray(props.marks) && props.marks.length > 0) {
    return {
      replyCount: props.marks[0] || 0,
      likeCount: props.marks[1] || 0,
      dislikeCount: props.marks[2] || 0,
    };
  }
});


function view(){
    emit('commentView')
}
</script>

<style lang="scss" scoped>
.marksBox {
    width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .iconNum {
    display: flex;
    align-items: center;
    margin-right: 10px;
    .icon {
      font-size: 20px;
      margin-right: 4px;
    }
    .num {
      font-size: 14px;
      line-height: 20px;
      min-width: 1.5em;
      padding: 0;
    }
    .event {
      font-size: 14px;
      line-height: 20px;
      // margin-left: 4px;
      cursor: pointer;
    }
  }
}
</style>
