<template>
  <div class="contentBox">
    <el-tooltip class="cell-tooltip" :content="type === '1' ?  props.row.theaterContent : props.row.content" placement="top" popperClass="tooltipH" rawContent>
      <div class="text" v-if="type === '1'">{{ props.row.theaterContent?.replace(/<[^>]+>/g, '') }}</div>
      <div class="text" v-else>{{ props.row.content?.replace(/<[^>]+>/g, '') }}</div>
    </el-tooltip>

    <div class="markList">
      <MarkList :marks="[props.row.replyCount, props.row.likeCount, props.row.dislikeCount]" @commentView="commentView" />
    </div>
  </div>
</template>

<script setup name="content">
import MarkList from './markList.vue'
const emit = defineEmits()
const props = defineProps({
  row: {
    type: Object,
  },
  type: {
    type: String,
    default: ' 0',
  },
})

function commentView() {
  // console.log('view')
  if (props.row.replyCount > 0) {
    emit('view')
  }
}
</script>

<style lang="scss" scoped>
.contentBox {
  overflow: hidden;
  .text {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
