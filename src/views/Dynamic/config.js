import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
export const tableConfig = {
  items: [
    { label: '标题', prop: 'title', type: 'string', option: {} },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    // { label: '动态详情', prop: 'body', type: 'string', option: {} },
    {
      label: '封面',
      prop: 'coverImage',
      type: 'Image',
      option: {
        minWidth: 220,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'string', option: {} },
    {
      label: '状态',
      prop: 'status',
      type: 'Switch',
      option: {
        tipName: 'title',
      },
    },
  ],
}
export const formConfig = {
  items: [
    { label: '动态标题', prop: 'title', type: 'String', option: {}, size: 'default' },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {}, size: 'small' },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {}, size: 'small' },
    {
      label: '动态详情',
      prop: 'body',
      type: 'String',
      option: {
        type: 'textarea',
        rows: '6',
      },
      size: 'default',
    },
    {
      label: '动态图片',
      prop: 'image',
      type: 'Cover',
      option: {
        vals: ['coverImage', 'images'],
      },
      size: 'default',
    },
  ],
  size: 'default',
}

export const queryConfig = {
  items: [
    {
      label: '关联剧目',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '关联剧场',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
