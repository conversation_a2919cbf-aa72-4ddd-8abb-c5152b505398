<template>
  <div class="app-container">
    <HandleRow v-model:query="queryParams" @search="getList" :showSearch="false" :needTab="true" />

    <!-- 分组 -->
    <CommonTable
      :listReq="$api.userGroup.userTicketGroup"
      :queryParams="queryParams"
      :tableCols="tableCols1"
      v-model:multiple="multiple"
      ref="tableRef"
      v-if="queryParams.tableType === 0">
      <template #repertoireNameList="{ data }">
        <p v-for="(i, index) in data.scope.row.repertoireNameList">{{ i }}</p>
      </template>
    </CommonTable>

    <!-- 榜单 -->
    <CommonTable
      :listReq="$api.userGroup.userLeaderboard"
      :queryParams="queryParams"
      :tableCols="tableCols2"
      v-model:multiple="multiple"
      :noGrow="true"
      ref="tableRef"
      v-else-if="queryParams.tableType === 1">
      <template #comment>
        <div class="commentWrap" v-if="queryParams.tableType === 1">
          <div class="title">说点什么：</div>
          <div class="commentTxt">{{ commentCon }}</div>
        </div>
      </template>
    </CommonTable>
  </div>
</template>

<script setup name="Answer">
import { tableConfig1, tableConfig2 } from './config'
const { proxy } = getCurrentInstance()

const route = useRoute()

/* 查询和表格配置 */
const queryParams = ref({
  tableType: 0,
  pageNum: 1,
  pageSize: 10,
  userId: route.query.id,
})

const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(false)
const tableCols1 = ref(tableConfig1.items)
const tableCols2 = ref(tableConfig2.items)

const commentCon = ref('')

onMounted(() => {
  handleGetComment()
})

const handleGetComment = () => {
  proxy.$api.userGroup
    .userLeaderboardComment({
      userId: route.query.id,
    })
    .then((res) => {
      if (res.data && res.data.length) commentCon.value = res.data[res.data.length - 1].commentText
    })
}
</script>

<style scoped lang="scss">
.commentWrap {
  flex-grow: 1;
  margin-top: 40px;

  .title {
    color: #21252b;
    font-weight: 500;
    font-size: 16px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    line-height: 24px;
  }

  .commentTxt {
    box-sizing: border-box;
    margin-top: 10px;
    padding: 10px;
    border-radius: 0 20px 20px 20px;
    background: #f4f6f9;
    color: #69717a;
    font-weight: 400;
    font-size: 14px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    line-height: 20px;
    max-width: 400px;
  }
}
</style>
