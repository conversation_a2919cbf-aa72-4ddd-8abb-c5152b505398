<template>
    <div class="answerBox">
        <div class="answerList" v-if="form.length">
            <div class="answerItem" v-for="item in form" :key="item.id">
                <el-card shadow="hover" :body-style="{ padding: '20px' }">
                    <template #header>
                        <div class="answerHeader">
                            <div class="name">
                                <span>{{ item.repertoireName }}</span>
                            </div>
                            <div class="text">
                                <span>演出场地</span>
                                <span>{{ item.theaterName }}</span>
                            </div>
                            <div class="text">
                                <span>演出场次</span>
                                <span>{{ item.startTime + '-' + item.endTime }}</span>
                            </div>
                        </div>
                    </template>
                    <div class="answerInfo">
                        <div class="queBox item">
                            <div class="prefix">
                                <div class="prefixIcon queIcon">Q</div>
                            </div>
                            <div class="info">
                                <div class="text1">
                                    <span class="name">{{ item.userName }}</span>
                                    <span class="time">{{ item.createTime }}</span>
                                </div>
                                <div class="text2">
                                    <div class="content">{{ item.content }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="ansBox item" v-for="ans in item.issueReplyResponseList" :key="ans.id">
                            <div class="prefix">
                                <div class="prefixIcon ansIcon">A</div>
                            </div>
                            <div class="info">
                                <div class="text1">
                                    <span class="name">{{ ans.replyName }}</span>
                                    <span class="time">{{ ans.createTime }}</span>
                                </div>
                                <div class="text2">
                                    <div class="content">{{ ans.content }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
                
            </div>
        </div>
        <div class="table-empty" v-else>
            暂无数据
        </div>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="answer">
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance()
const props = defineProps({
    id:{
        type:Number
    }
})
const queryParams = ref({
    pageNum:1,
    pageSize:10,
    userId: props.id
})
const form = ref([

])

const total = ref(0)
const getList = () => {
    proxy.$api.issue.listByUserId(queryParams.value).then(res => {
        console.log('answer', res)
        form.value = res.data.rows
        total.value = res.data.total
    })
}

onMounted(() => {
    getList()
})

</script>

<style lang="scss" scoped>
.answerBox{
    .answerList{
        .answerItem{
            margin-bottom: 20px;
            .answerHeader{
                display: flex;
                align-items: center;
                height: 30px;
                padding: 0 5px;
                .name{
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 30px;
                    margin-right: 20px;
                }
                .text{
                    margin-right: 20px;
                    span{
                        margin-right: 10px;
                        &:last-child{
                            margin-right: 0;
                        }
                    }
                }
            }
            .answerInfo{
                .item{
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                    line-height: 24px;
                    .prefix{
                        flex-shrink: 0;
                        margin-right: 10px;
                        .prefixIcon{
                            width: 36px;
                            height: 36px;
                            border-radius: 50%;
                            overflow: hidden;
                            text-align: center;
                            line-height: 36px;
                            // font-weight: 600;
                            color: #fff;
                            font-size: 20px;
                            cursor: default;
                        }
                        .queIcon{
                            background-color: var(--el-color-warning);
                        }
                        .ansIcon{
                            background-color: var(--el-color-success);
                        }
                    }
                    .info{
                        flex-grow: 1;
                        width: 0;
                        .text1{
                            display: flex;
                            align-items: center;
                            .name{
                                margin-right: 22px;
                                font-weight: 600;
                            }
                        }
                    }
                }
                .queBox{
                    margin-bottom: 12px;
                }
                .ansBox{
                    padding-left: 46px;
                }
            }
        }
    }
}
</style>