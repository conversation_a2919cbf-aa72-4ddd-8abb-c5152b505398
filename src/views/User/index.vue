<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户名称 / 账号">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>
    <CommonTable :listReq="$api.user.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <template #group="{ data }">
        <el-button link type="primary" @click="router.push({ path: '/group', query: { id: data.scope.row.id } })">查看详情</el-button>
      </template>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          <BtnForChangePw :rowId="scope.row.id" />
        </template>
      </el-table-column>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <CommonForm v-model:form="dialogControl.form" :formOptions="formOptions" :readonly="dialogControl.readonly" ref="formRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { formConfig, queryConfig, tableConfig } from './config'
const { proxy } = getCurrentInstance()

const router = useRouter()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {
    sex: 0,
    status: 1,
  }
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加用户')
}
function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.user.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.user.detail, row, dialogControl.value, '修改用户信息')
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.user.remove, row)
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs['formRef'].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(proxy.$api.user.add, proxy.$api.user.update, dialogControl.value)
    }
  })
}
</script>
