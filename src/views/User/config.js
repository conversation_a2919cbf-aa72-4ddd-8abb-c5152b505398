import { useDict } from '@/utils/dict'
const { sys_user_sex, status_flag } = useDict('sys_user_sex', 'status_flag')

export const tableConfig = {
  items: [
    // { label: '编号', prop: 'no', type: 'string', option: {} },
    { label: '用户名称', prop: 'name', type: 'String', option: {} },
    { label: '手机号', prop: 'phone', type: 'String', option: {} },
    {
      label: '用户性别',
      prop: 'sex',
      type: 'Dict',
      option: {
        pull: sys_user_sex,
      },
    },
    {
      label: '头像',
      prop: 'avatar',
      type: 'Avatar',
      option: {
        minWidth: 120,
      },
    },

    {
      label: '状态',
      prop: 'status',
      type: 'Dict',
      option: {
        pull: status_flag,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    { label: '榜单分组', prop: 'group', type: 'Slot', option: {} },
  ],
}

export const formConfig = {
  items: [
    { label: '用户名称', prop: 'name', type: 'String', option: {}, rules: [{ required: true, message: '请输入用户名称', trigger: '' }], size: 'default' },
    {
      label: '手机号',
      prop: 'phone',
      type: 'String',
      option: {},
      rules: [
        { required: true, message: '请输入手机号', trigger: '' },
        { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号', trigger: '' },
      ],
      size: 'default',
    },
    {
      label: '头像',
      prop: 'avatar',
      type: 'Image',
      option: {
        limit: 1,
      },
      size: 'default',
    },
    {
      label: '用户密码',
      prop: 'password',
      type: 'Password',
      option: {},
      rules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (value && value.startsWith('$') && value.length > 30) {
              callback()
            }
            const rexg = /^[a-zA-Z0-9]{1,11}$/
            if (!value) {
              callback(new Error('请输入用户密码'))
            } else if (!rexg.test(value)) {
              callback(new Error('请输入11位以内的英文字母或数字'))
            } else {
              callback()
            }
          },
          trigger: '',
        },
      ],
      size: 'default',
    },
    {
      label: '用户性别',
      prop: 'sex',
      type: 'Radio',
      option: {
        pull: sys_user_sex,
      },
      size: 'small',
    },
    {
      label: '状态',
      prop: 'status',
      type: 'Radio',
      option: {
        pull: status_flag,
      },
      size: 'small',
    },
  ],
  size: 'default',
}

export const queryConfig = {
  items: [
    {
      label: '状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: status_flag,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
