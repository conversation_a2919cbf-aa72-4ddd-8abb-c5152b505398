<template>
  <div class="newsListBox">
    <div class="newsList">
      <el-card
        class="news"
        shadow="hover"
        v-for="item in form"
        :key="item.id"
        @click="handleView(item)"
      >
        <div class="imgList">
          <el-image
            v-if="item.coverImage"
            :src="baseUrl + item.coverImage"
            fit="fill"
            class="img"
          ></el-image>
          <el-image
            :src="baseUrl + img"
            fit="fill"
            class="img"
            v-for="(img, index) in changeImages(item.images)"
            :key="index"
          ></el-image>
        </div>
        <div class="name" :title="item.title">
          {{ item.title }}
        </div>
        <div class="info">
          {{ item.body }}
          <!-- <LineWrap :lineNum="4" :lineContent="item.body"/> -->
        </div>

        <div class="footer">
          <div class="time">
            {{ item.createTime }}
          </div>
          <div class="star">
            <svg-icon class-name="love" icon-class="love" />
            <div class="num">{{ item.likeCount }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize('default')"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >
      <CommonForm
        v-model:form="dialogControl.form"
        :formOptions="formConfig"
        :readonly="dialogControl.readonly"
        ref="formRef"
      />
    </el-dialog>
  </div>
</template>

<script setup name="newsList">
import { getCurrentInstance, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  repertoireId: {
    type: Number,
    default: undefined,
  },
});

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const queryParams = ref({
  pageNum: 1,
  pageSize: 6,
  repertoireId: props.repertoireId || undefined,
});

const form = ref({});
const total = ref(0);

const getList = () => {
  proxy.$api.dynamic.listByPage(queryParams.value).then((res) => {
    form.value = res.data.rows;
    total.value = res.data.total;
  });
};

function changeImages(imgs) {
  if(imgs.length == 0) return []
  return imgs.split(",").slice(0, 2);
}

/* 详情 */
/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  form: {},
  readonly: false,
});
function handleView(row) {
  dialogControl.value.readonly = true;
  // proxy.$refs.tableRef.handleGetDetail(proxy.$api.dynamic.detail, row, dialogControl.value, '查看详情')
  proxy.$api.dynamic.detail({ id: row.id }).then((res) => {
    dialogControl.value.form = res.data;
    dialogControl.value.open = true;
  });
}
const formConfig = ref([
    {
      label: "动态标题",
      prop: "title",
      type: "String",
      option: {},
      size: "default",
    },
    {
      label: "关联剧目",
      prop: "repertoireName",
      type: "String",
      option: {},
      size: "small",
    },
    {
      label: "关联剧场",
      prop: "theaterName",
      type: "String",
      option: {},
      size: "small",
    },
    {
      label: "动态详情",
      prop: "body",
      type: "String",
      option: {
        type: "textarea",
        rows: "6",
      },
      size: "default",
    },
    {
      label: "动态图片",
      prop: "image",
      type: "Cover",
      option: {
        vals: ["coverImage", "images"],
      },
      size: "default",
    },
  ]);

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.newsListBox {
  .newsList {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-right: -10px;
    .news {
      width: 32%;
      position: relative;
      height: 300px;
      margin-bottom: 10px;
      margin-right: 14px;
      :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .imgList {
        display: flex;
        width: 100%;
        margin-bottom: 10px;
        // margin-right: -8px;
        // justify-content: space-between;
        .img {
          width: 100px;
          height: 100px;
          margin: 0 4px;
        }
      }
      .name {
        color: rgba(16, 16, 16, 1);
        font-size: 16px;
        text-align: left;
        font-family: SourceHanSansSC-medium;
        line-height: 30px;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .info {
        line-height: 22px;
        flex-grow: 1;
        word-wrap: break-word;
        height: 0;
        overflow: hidden;
        text-overflow: ellipsis;
          display:-webkit-box;
          -webkit-line-clamp:4;	//元素几行显示
          -webkit-box-orient:vertical;
      }
      .footer {
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin-top: 16px;
        .star {
          display: flex;
          align-items: center;
          .love {
            font-size: 20px;
          }
          .num {
            line-height: 20px;
            // min-width: 20px;
            // text-align: center;
            padding-left: 10px;
          }
        }
      }
    }
  }
}
</style>
