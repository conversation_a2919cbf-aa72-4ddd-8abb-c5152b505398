<template>
  <div class="basicInfoBox">
    <!-- <section class="section">
            <label class="label">演出场次</label>
            <content class="content">
                <el-row :gutter="20" class="tables" v-if="sessionLength">
                    <el-col :span="12" class="table">
                        <el-row :gutter="20" class="header">
                            <el-col :span="6">演出市场</el-col>
                            <el-col :span="18">演出时间</el-col>
                        </el-row>
                        <el-row :gutter="20" class="row" v-for="(item, index) in form4" :key="index">
                            <el-col :span="6">{{ item.theaterName }}</el-col>
                            <el-col :span="18">{{ item.startTime }} ——— {{ item.endTime }}</el-col>
                        </el-row>
                    
                    </el-col>
                    <el-col :span="12" class="table">
                        <el-row :gutter="20" class="header">
                            <el-col :span="6">演出市场</el-col>
                            <el-col :span="18">演出时间</el-col>
                        </el-row>
                        <el-row :gutter="20" class="row" v-for="(item, index) in form5" :key="index">
                            <el-col :span="6">{{ item.theaterName }}</el-col>
                            <el-col :span="18">{{ item.startTime }} ——— {{ item.endTime }}</el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <el-row class="tables" v-else>
                    <span style="color: #909399;">暂无场次信息</span>
                </el-row>
            </content>
        </section> -->
    <section class="section">
      <label class="label">剧目简介</label>
      <content class="content">
        <el-row :gutter="20" class="tables">
          <div style="padding: 0 10px">
            {{ htmlToStr(introduction) || '暂无简介' }}
          </div>
        </el-row>
      </content>
    </section>
    <section class="section">
      <label class="label">演员信息</label>
      <content class="content">
        <div class="wrap">
          <div class="label">主要角色信息</div>
          <div class="list">
            <el-row :gutter="20" class="row" v-for="(item, index) in form1" :key="index">
              <el-col :span="2" class="indexRow">{{ index + 1 }}</el-col>
              <el-col :span="4">
                <div class="text">
                  <DefaultAvatar :url="item.picture" :size="100" />
                </div>
              </el-col>
              <el-col :span="4" class="textRow">
                <div class="text">
                  <label>角色名称</label>
                  <div>{{ item.roleName || '无' }}</div>
                </div>
                <div class="text">
                  <label>演员姓名</label>
                  <div>{{ item.actorName || '无' }}</div>
                </div>
              </el-col>
              <el-col :span="14" class="textRow">
                <div class="text">
                  <label>角色简介</label>
                  <div>{{ item.introduction || '无' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="wrap">
          <div class="label">群演信息</div>
          <div class="list">
            <el-row :gutter="20" class="row" v-for="(item, index) in form2" :key="index">
              <el-col :span="2" class="indexRow">{{ index + 1 }}</el-col>
              <el-col :span="8" class="textRow">
                <div class="text">
                  <label>群演角色名称</label>
                  <div>{{ item.roleName || '无' }}</div>
                </div>
              </el-col>
              <el-col :span="14" class="textRow">
                <div class="text">
                  <label>群演姓名</label>
                  <div>{{ item.groupPerformanceName || '无' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </content>
    </section>
    <section class="section">
      <label class="label">主创团队信息</label>
      <content class="content">
        <div class="wrap">
          <div class="list">
            <el-row :gutter="20" class="row" v-for="(item, index) in form3" :key="index">
              <el-col :span="2" class="indexRow">{{ index + 1 }}</el-col>
              <el-col :span="4">
                <div class="text">
                  <DefaultAvatar :url="item.picture" :size="100" />
                </div>
              </el-col>
              <el-col :span="4" class="textRow">
                <div class="text">
                  <label>主创姓名</label>
                  <div>{{ item.name || '无' }}</div>
                </div>
                <div class="text">
                  <label>职能名称</label>
                  <div>{{ item.functionName || '无' }}</div>
                </div>
              </el-col>
              <el-col :span="14" class="textRow">
                <div class="text">
                  <label>主创简介</label>
                  <div>{{ item.introduction || '无' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </content>
    </section>
  </div>
</template>

<script setup name="basicInfo">
import { getCurrentInstance, onMounted } from 'vue'
const { proxy } = getCurrentInstance()
const { id, introduction } = defineProps({
  id: {
    type: Number,
    default: 0,
  },
  introduction: {
    type: String,
    default: undefined,
  },
})

/* 演员 */

const form1 = ref({})

const form2 = ref({})

/* 主创 */
const form3 = ref({})

/* 场次 */
const form4 = ref({})

const form5 = ref({})

const sessionLength = ref(0)

onMounted(() => {
  proxy.$api.repertoireActor
    .listByRepertoireId({
      repertoireId: id,
      actorType: 1,
    })
    .then((res) => {
      form1.value = res.data
    })
  proxy.$api.repertoireActor
    .listByRepertoireId({
      repertoireId: id,
      actorType: 2,
    })
    .then((res) => {
      form2.value = res.data
    })

  proxy.$api.repertoireCreativeTeam
    .listByRepertoireId({
      repertoireId: id,
    })
    .then((res) => {
      form3.value = res.data
    })
})
</script>

<style lang="scss" scoped>
.basicInfoBox {
  padding: 0 20px;
  .section {
    margin: 40px 0;
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
    .label {
      margin-bottom: 15px;
      display: block;
    }
    .content {
      .tables {
        padding: 0 20px;
        .header {
          font-weight: 600;
        }
        .row {
          margin-top: 10px;
        }
      }
      .wrap {
        padding: 0 20px;
      }
      .list {
        .row {
          margin-bottom: 30px;
          :deep(.el-col) {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
          }
          .indexRow {
            align-items: center;
            justify-content: center;
          }
          .textRow {
            padding: 10px 0;
          }
          .text {
            display: flex;
            align-items: flex-start;
            label {
              flex-shrink: 0;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
