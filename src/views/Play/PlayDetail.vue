<template>
  <div class="intro">
    <div class="cover">
      <div class="img">
        <!-- <el-image :src="form.cover.imgList[form.cover.pageNum - 1]" fit="contain" style="width:100%;height:100%"></el-image> -->
        <el-carousel height="240px" :loop="false" :autoplay="false" arrow="never" indicator-position="none" ref="carouselRef">
          <el-carousel-item v-for="(item, index) in form.cover.imgList" :key="index">
            <div class="tag" v-if="index == 0">封面</div>
            <el-image :src="item" preview-teleported :preview-src-list="[item]" fit="cover" style="width: 100%; height: 100%"></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="control">
        <div class="pagination">
          <el-button class="button" size="mini" @click="imgPrev()" :disabled="form.cover.pageNum === 1">上一页</el-button>
          <span class="page-text">
            {{ form.cover.pageNum + ' / ' + form.cover.pageSize }}
          </span>
          <el-button class="button" size="mini" @click="imgNext()" :disabled="form.cover.pageNum === form.cover.pageSize">下一页</el-button>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="title">
        <div class="tags" v-if="form.repertoireLabel">
          <!-- <div class="tag" v-for="item in ">{{ form.repertoireLabel }}</div> -->
          <div class="tag" v-for="(tag, index) in form.tags" :key="index">
            {{ tag }}
          </div>
        </div>
        <div class="name-section">
          <div class="name" @dblclick="openEditDialog">
            {{ form.name }}
            <div @click="openEditDialog" class="edit-btn">
              <el-icon>
                <Edit />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="text">
        <span class="label">所属商家：</span>
        <span class="value"> {{ form.merchantName }} </span>
      </div>
      <div class="text">
        <div class="label">剧目简介：</div>
        <el-row class="tables">
          <!-- {{ htmlToStr(form.introduction) }} -->

          <el-col :div="24" class="table" v-if="sessionLength">
            <el-row :gutter="20" class="header">
              <el-col :span="8">演出场地</el-col>
              <el-col :span="16">演出时间</el-col>
            </el-row>
            <el-scrollbar height="180">
              <el-row :gutter="20" class="row" v-for="(item, index) in sessionList" :key="index">
                <el-col :span="8">{{ item.theaterName }}</el-col>
                <el-col :span="16">{{ item.startTime }} ——— {{ item.endTime }}</el-col>
              </el-row>
            </el-scrollbar>
          </el-col>
          <el-col :span="24" class="table" v-else>暂无演出场次</el-col>
        </el-row>
      </div>
    </div>
  </div>
  <div class="table-list">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first">
        <BasicInfo :id="props.form.id" :introduction="form.introduction" />
      </el-tab-pane>
      <!-- <el-tab-pane label="电子票" name="second">
                <Bill :repertoire-id="props.form.id" :disable-search="true"/>
            </el-tab-pane>
            <el-tab-pane label="数字头像" name="third">
                <Avatar :repertoire-id="props.form.id" :disable-search="true"/>
            </el-tab-pane> -->
      <el-tab-pane label="商品信息" name="third">
        <Goods :repertoire-id="props.form.id" :disable-search="true" />
      </el-tab-pane>
      <el-tab-pane label="等级勋章" name="fourth">
        <Medal :repertoire-id="props.form.id" :disable-search="true" />
      </el-tab-pane>
      <el-tab-pane label="问大家" name="five">
        <AskList :repertoire-id="props.form.id" merchant-category="1" />
      </el-tab-pane>
      <el-tab-pane label="剧目动态" name="sixth">
        <NewsList :repertoire-id="props.form.id" />
      </el-tab-pane>
    </el-tabs>
  </div>

  <!-- 编辑名称对话框 -->
  <el-dialog v-model="editDialogVisible" width="820px" top="30vh" :close-on-click-modal="false" @close="cancelNameEdit" class="edit-name-dialog" align-center :show-close="false">
    <template #header>
      <div class="dialog-header">
        <div class="header-icon">
          <el-icon size="20">
            <Edit />
          </el-icon>
        </div>
        <div class="header-content">
          <h3 class="dialog-title">编辑剧目名称</h3>
          <p class="dialog-subtitle">修改剧目的显示名称</p>
        </div>
      </div>
    </template>

    <div class="dialog-body">
      <div class="form-container">

        <el-form :model="editForm" :rules="editRules" ref="editFormRef" class="edit-form">
          <el-form-item prop="name" class="name-input-item">
            <el-input v-model="editForm.name" placeholder="请输入剧目名称" maxlength="50" show-word-limit @keyup.enter="saveNameEdit" size="large" class="name-input" />
          </el-form-item>
        </el-form>

      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="large" @click="cancelNameEdit" class="cancel-btn">
          <el-icon>
            <Close />
          </el-icon>
          取消
        </el-button>
        <el-button type="primary" size="large" @click="saveNameEdit" :loading="saving" class="save-btn">
          <el-icon v-if="!saving">
            <Check />
          </el-icon>
          {{ saving ? '保存中...' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="theaterDetail">
import img2 from '@/assets/images/avatar1.jpg'
import img1 from '@/assets/images/profile.jpg'
import { Check, Close, Edit } from '@element-plus/icons-vue'
import Goods from '../Assets/Goods/index.vue'
import Medal from '../Medal/index.vue'
import AskList from '../Theater/detail/askList.vue'
import BasicInfo from './detail/basicInfo.vue'
import NewsList from './detail/newsList.vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['nameUpdated'])
const form = ref({
  name: '上海大剧院',
  cover: {
    imgList: [img1, img2],
    pageNum: 1,
    pageSize: 2,
  },
})

// 编辑名称相关状态
const editDialogVisible = ref(false)
const saving = ref(false)
const editFormRef = ref(null)
const editForm = ref({
  name: ''
})
const editRules = {
  name: [
    { required: true, message: '请输入剧目名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  form.value.name = props.form.name
  form.value.repertoireLabel = props.form.repertoireLabel
  form.value.merchantName = props.form.merchantName
  form.value.introduction = props.form.introduction

  let imgArr = []
  if (props.form.pictures) {
    imgArr = [props.form.coverPicture, ...props.form.pictures.split(',')]
  } else {
    imgArr = [props.form.coverPicture]
  }

  form.value.cover.imgList = imgArr.map((i) => {
    return proxy.addBaseUrl(i)
  })
  form.value.cover.pageSize = imgArr.length
  form.value.cover.pageNum = 1

  form.value.tags = props.form.repertoireLabel ? props.form.repertoireLabel.split(',') : []

  getSession()
})

function imgPrev() {
  proxy.$refs['carouselRef'].prev()
  form.value.cover.pageNum--
}
function imgNext() {
  proxy.$refs['carouselRef'].next()
  form.value.cover.pageNum++
}

// 编辑名称相关方法
function openEditDialog() {
  editForm.value.name = form.value.name
  editDialogVisible.value = true
}

function cancelNameEdit() {
  editDialogVisible.value = false
  editForm.value.name = ''
  saving.value = false
  // 重置表单验证
  nextTick(() => {
    editFormRef.value?.clearValidate()
  })
}

async function saveNameEdit() {
  // 表单验证
  const valid = await editFormRef.value?.validate().catch(() => false)
  if (!valid) return

  if (editForm.value.name.trim() === form.value.name) {
    cancelNameEdit()
    return
  }

  saving.value = true
  try {
    await proxy.$api.repertoire.update({
      id: props.form.id,
      name: editForm.value.name.trim()
    }, {
      headers: { repeatSubmit: false }
    })

    form.value.name = editForm.value.name.trim()
    proxy.$modal.msgSuccess('剧目名称修改成功')
    cancelNameEdit()

    // 触发父组件更新
    emit('nameUpdated', {
      id: props.form.id,
      name: editForm.value.name.trim()
    })
  } catch (error) {
    console.error('修改剧目名称失败:', error)
    if (error.message !== '数据正在处理，请勿重复提交') {
      proxy.$modal.msgError('修改失败，请重试')
    }
  } finally {
    saving.value = false
  }
}
const sessionLength = ref(0)
const sessionList = ref([])
function getSession() {
  proxy.$api.repertoireInfoDetail
    .listByRepertoireId({
      repertoireId: props.form.id,
    })
    .then((res) => {
      // form4.value = res.data
      sessionLength.value = res.data.length
      // const num = Math.ceil(sessionLength.value/2)
      console.log('---', form, res.data)
      sessionList.value = res.data
    })
}

const activeName = 'first'
</script>

<style lang="scss" scoped>
.intro {
  width: 100%;
  display: flex;
  .cover {
    width: 420px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    .img {
      width: 100%;
      height: 240px;
      flex-shrink: 0;
      position: relative;
      .tag {
        width: 42px;
        height: 25px;
        border-radius: 4px;
        background-color: rgba(253, 187, 44, 0.8);
        color: rgba(255, 255, 255, 1);
        font-size: 14px;
        text-align: center;
        font-family: Microsoft Yahei;
        line-height: 25px;
        position: absolute;
        right: 16px;
        top: 8px;
        z-index: 100;
      }
    }
    .control {
      width: 100%;
      height: 50px;
      padding: 9px 0;
      text-align: center;
      .pagination {
        .button {
          height: 32px;
          padding: 0 10px;
        }
        .page-text {
          margin: 0 20px;
        }
      }
    }
  }
  .info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    // height: 100%;
    height: 240px;
    padding: 0 20px;
    .title {
      display: flex;
      .tags {
        display: flex;
        margin-right: 6px;
        .tag {
          background-color: rgba(253, 187, 44, 0.8);
          color: #fff;
          border-radius: 4px;
          color: rgba(255, 255, 255, 1);
          font-size: 14px;
          text-align: center;
          font-family: Microsoft Yahei;
          padding: 4px 8px;
          margin-right: 10px;
        }
      }
      .name-section {
        .name {
          color: rgba(16, 16, 16, 1);
          font-size: 20px;
          text-align: left;
          font-family: SourceHanSansSC-medium;
          display: flex;
          align-items: center;
          cursor: pointer;
          .edit-btn {
            margin-left: 8px;
            opacity: 1; // 常显示编辑按钮
            transition: opacity 0.3s;
            color: #409eff;
          }
        }
      }
    }
    .text {
      line-height: 20px;
      margin-top: 15px;
      display: flex;
      .label {
        font-weight: 600;
      }
      .value {
      }
      .tables {
        flex-grow: 1;
        .header {
          font-weight: 600;
        }
        .row {
          margin-top: 10px;
        }
        .table {
          .el-row {
            padding: 0 !important;
            margin: 0 !important;
            margin-top: 10px !important;
            .el-col {
              // padding: 0 !important;
            }
          }
        }
      }
    }
  }
}
.table-list {
  margin-top: 16px;
  max-height: calc(88vh - 410px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .el-tabs {
    // height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
    :deep(.el-tabs__content) {
      flex-grow: 1;
      overflow: auto;
      .app-container {
        padding: 0;
      }
    }
  }
}

// 编辑名称弹框样式
:deep(.edit-name-dialog) {
  .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;

  .header-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
  }

  .header-content {
    flex: 1;

    .dialog-title {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      line-height: 1.2;
    }

    .dialog-subtitle {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.2;
    }
  }
}

.dialog-body {
  padding: 24px 10px;
  box-sizing: border-box;

  .edit-form {
    .name-input-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #333;
        font-size: 15px;
      }

      .name-input {
        :deep(.el-input__wrapper) {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          border: 1px solid #d9d9d9;
          height: 60px;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          &.is-focus {
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
          }
        }
      }

      .input-tip {
        display: flex;
        align-items: center;
        margin-top: 8px;
        font-size: 13px;
        color: #666;

        .el-icon {
          margin-right: 6px;
          color: #ff6b6b;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-around;
  gap: 12px;

  .cancel-btn {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    color: #666;

    &:hover {
      border-color: #ff6b6b;
      color: #ff6b6b;
    }
  }

  .save-btn {
    border-radius: 8px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    min-width: 120px;

    &:hover {
      background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
    }

    &.is-loading {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }
  }
}
</style>
