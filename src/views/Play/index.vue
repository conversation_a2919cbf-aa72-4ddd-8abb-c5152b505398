<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧目名称">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
          <!-- <el-button @click="handleConall" icon="Filter" plain type="info">打印全部数据</el-button> -->
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :listReq="$api.repertoire.listByPage" :queryParams="queryParams" :tableCols="tableCols" :statusReq="$api.repertoire.updateStatus" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <div>
            <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </div>
          <div>
            <BtnForCheck :req="$api.repertoire.audit" :disabled="scope.row.audit" :rowId="scope.row.id" hasPassEvent @pass="handlePass(scope.row)" @change="getList" />
            <el-button link type="warning" :disabled="scope.row.audit !== 2" @click="handleRecommend(scope.row, scope.row.recommend == 0 ? '推荐成功' : '推荐已取消')">{{ scope.row.recommend == 0 ? '推荐' : '已推荐' }}</el-button>

            <BtnForLabel v-if="scope.row" :rowId="scope.row.id" :labels="scope.row.repertoireLabel" @change="getList" />
          </div>

          <!-- <el-button link type="danger" @click="handleCon(scope.row)">打印数据</el-button> -->
        </template>
      </el-table-column>

      <template #repertoire="{ data }">
        <PlayInfo :form="data.scope.row" />
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="1200" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
      <!-- <CommonForm v-model:form = "form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef"/> -->
      <PlayDetail :form="dialogControl.form" @nameUpdated="handleNameUpdated" />
    </el-dialog>

    <!-- 添加或修改公告对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl2.title" v-model="dialogControl2.open" :width="480" append-to-body destroy-on-close :close-on-click-modal="false" draggable @closed="cancel" ref="dialogRef">
      <el-form :model="dialogControl2.form" label-width="7em" ref="formRef" label-position="top" class="auditForm">
        <div class="title">{{ dialogControl2.form.name }}</div>
        <el-form-item label="" size="normal">
          <el-input v-model="dialogControl2.form.shortName" placeholder="请输入剧目短标题" size="normal" clearable @change=""></el-input>
        </el-form-item>
        <div class="tips">
          <div>示例：</div>
          <div>
            <p>环境式推理悬疑剧《蔷薇之门》——蔷薇之门</p>
            <p>环境式《蔷薇之恋》推理悬疑剧《蔷薇之门》——蔷薇之恋,蔷薇之门</p>
            <p>环境式推理悬疑剧蔷薇之门——蔷薇之门</p>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 认</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Play">
import PlayDetail from './PlayDetail.vue'
import { formConfig, queryConfig, tableConfig } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {}
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}

function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.repertoire.findRepertoireInfo, row, dialogControl.value, '查看详情')
}

function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.repertoire.remove, row)
}

function handleRecommend(row, text) {
  proxy.$api.repertoire.updateRecommend(row.id).then((res) => {
    proxy.$modal.msgSuccess(text)
    getList()
  })
}

const dialogControl2 = ref({
  open: false,
  title: '剧目审核',
  form: {
    audit: '2',
    id: '',
    shortName: '',
    reasonsRejection: '',
  },
  readonly: false,
})
function handlePass(row) {
  // console.log(11)
  dialogControl2.value.form.name = row.name
  dialogControl2.value.form.id = row.id
  dialogControl2.value.form.shortName = ''
  dialogControl2.value.open = true
}
function submitForm() {
  proxy.$modal
    .confirm('是否确认审核通过？')
    .then((res) => {
      // console.log(33)
      proxy.$api.repertoire.audit(dialogControl2.value.form).then((res) => {
        proxy.$modal.msgSuccess('已通过')
        getList()
        dialogControl2.value.open = false
      })
    })
    .catch((error) => { })
}

function handleNameUpdated(data) {
  // 更新表格中的数据
  dialogControl.value.form.name = data.name
  // 刷新表格数据
  getList()
}
</script>

<style lang="scss" scoped>
.auditForm {
  .title {
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 10px;
  }
  .tips {
    color: var(--el-color-info);
    font-size: 14px;
    display: flex;
    div {
      &:first-child {
        flex-shrink: 0;
      }
      p {
        margin: 0;
      }
    }
  }
}
</style>
