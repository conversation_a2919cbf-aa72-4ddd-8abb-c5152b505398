import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { audit_flag, recommend_flag, status_flag } = useDict('audit_flag', 'recommend_flag', 'status_flag')

export const tableConfig = {
  items: [
    { label: '剧目ID', prop: 'id', type: 'String', option: {} },

    {
      label: '剧目信息',
      prop: 'repertoire',
      type: 'Slot',
      option: {
        minWidth: 500,
      },
    },
    {
      label: '二维码链接',
      prop: 'qrCode',
      type: 'QR',
      option: {
        minWidth: 160,
      },
    },
    {
      label: '是否禁用',
      prop: 'status',
      type: 'Switch',
      option: {
        tipName: 'name',
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'Audit',
      option: {
        pull: audit_flag,
        tip: 'reasonsRejection',
      },
    },

    {
      label: '是否推荐',
      prop: 'recommend',
      type: 'Dict',
      option: {
        pull: recommend_flag,
      },
    },
  ],
}

export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '所属商家',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: pull.merchantRepPull,
      },
    },
    {
      label: '剧目标签',
      prop: 'repertoireLabel',
      type: 'input',
      option: {},
    },
    {
      label: '关联剧场',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '是否禁用',
      prop: 'status',
      type: 'select',
      option: {
        list: status_flag,
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'multipleSelect',
      option: {
        pull: audit_flag,
      },
    },
    { label: '演出时间', prop: 'showTime', type: 'daterange', option: {} },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
