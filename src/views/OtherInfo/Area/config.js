import { useDict } from '@/utils/dict'

export const tableConfig = {
  items: [
    { label: '剧场名称', prop: 'name', type: 'String', option: {} },
    { label: '所属商家', prop: 'merchantName', type: 'String', option: {} },
    { label: '关注人数', prop: 'focusNumber', type: 'Integer', option: {} },
    { label: '剧场地址', prop: 'address', type: 'String', option: {} },
    { label: '封面图URL', prop: 'coverPicture', type: 'Image', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '审核状态',
      prop: 'status',
      type: 'Dict',
      option: {
        pull: [],
      },
    },
    {
      label: '是否推荐',
      prop: 'recommend',
      type: 'Dict',
      option: {
        pull: [],
      },
    },
  ],
}

export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '所属商家',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: [],
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'tags',
      option: {
        pull: [],
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
