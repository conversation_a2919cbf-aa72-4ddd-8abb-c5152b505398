<template>
  <div class="app-container">
    <el-row :gutter="20" class="contentRow">
      <!--部门数据-->
      <el-col :span="6" :xs="24" class="contentCol">
        <div class="treeTitle">
          <p class="titleTxt">区域信息</p>
          <el-button class="addSon" @click="addParent" plain type="warning" size="small"
            >添加省级</el-button
          >
          <el-button class="addSon" @click="addSon" plain type="primary" size="small"
            >添加市级</el-button
          >
        </div>
        <div class="head-container">
          <el-scrollbar>
            <el-tree
              :data="deptOptions"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :props="{ label: 'name', value: 'id', children: 'children' }"
              @current-change="handleNodeChange"
              @node-click="handleNodeClick"
              highlight-current
              node-key="id"
              ref="deptTreeRef"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="18" :xs="24" class="contentCol">
        <div class="formTitle">
          <p class="titleTxt">编辑/查看</p>
        </div>

        <el-form
          :model="form"
          :rules="areaInfoRules"
          class="formWrap formStyle1"
          hide-required-asterisk
          label-width="6em"
          ref="formRef"
        >
          <!-- <el-form-item class="formItem2" label="所属上级" prop="parentName">
              <el-input clearable placeholder="请输入所属上级" v-model="form.parentName" />
            </el-form-item> -->
          <el-form-item class="formItem2" label="区域编码" prop="code">
              <el-input clearable placeholder="请输入区域编码" v-model="form.id" />
            </el-form-item>
          <el-form-item class="formItem2" label="区域名称" prop="name">
            <el-input
              clearable
              placeholder="请输入区域名称"
              v-model="form.name"
            />
          </el-form-item>
          <el-form-item class="formItem2" label="排列顺序" prop="sort">
            <el-input-number :min="1" v-model="form.sort" />
          </el-form-item>
          <el-form-item class="formItem2" label="备注说明" prop="remark">
            <el-input
              :readonly="readOnly"
              clearable
              placeholder="请输入内容"
              rows="4"
              type="textarea"
              v-model="form.remark"
            />
          </el-form-item>
          <!-- <el-form-item class="formItem2" label="是否启用" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :key="dict.value" :label="dict.value" v-for="dict in satustype">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item> -->
          <el-form-item class="formItem2" label=" ">
            <el-button @click="submitForm" class="formBtn2" type="primary"
              >确定</el-button
            >
            <el-button @click="handleDel" class="formBtn2 btnCol" type="primary"
              >删除</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Area">
import { nextTick, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const open = ref(false); // 是否显示对话框
const readOnly = ref(false); // 是否只读
const title = ref(""); // 对话框标题
const form = ref({}); // 表单数据
const initForm = ref({});

const deptName = ref("");
const deptOptions = ref([]);
const deptTreeRef = ref(null);
const selectNode = ref(0);
const addChildStatus = ref(false);

/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

onMounted(() => {
  getDeptTree();
});

const addArea  = proxy.$api.area.add
const updateArea = proxy.$api.area.update
const delArea = proxy.$api.area.remove

/** 查询组织架构树结构 */
function getDeptTree(id) {
  proxy.$api.area.findAreaTree().then((res) => {
    deptOptions.value = res.data;
    if (id) {
      nextTick(() => {
        deptTreeRef.value.setCurrentKey(id);
      });
    } else {
      nextTick(() => {
        document.querySelector(".el-tree-node__content").click();
        // deptTreeRef.value.setCurrentKey(deptOptions.value[0].id);
      });
    }
  });
  
}

/** 节点改变事件 */
function handleNodeChange(data) {
  selectNode.value = data.id;
  proxy.$api.area.detail({ id: data.id }).then((response) => {
    form.value = proxy.deepClone(response.data);
    initForm.value = proxy.deepClone(response.data);
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        form.value.fullname = form.value.name
        updateArea(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getDeptTree(selectNode.value);
          })
          .catch((error) => {
            form.value = initForm.value;
          });
      } else {
        // addArea(form.value).then(response => {
        //   proxy.$modal.msgSuccess('新增成功')
        //   open.value = false
        //   getDeptTree(selectNode.value)
        // })
      }
    }
  });
}
/** 删除 */
function handleDel() {
  delArea(selectNode.value).then((response) => {
    proxy.$modal.msgSuccess("删除成功");
    getDeptTree(initForm.value.parentId);
  });
}
function addParent() {
  const childData = {
    children: [],
    id: undefined,
    name: "新的省份",
    fullname: "新的省份",
    code: "",
    parentId: 100000,
    remark: "",
    sort: 0,
    levelType: 1,
  };
  form.value = childData;
  addArea(form.value).then((response) => {
    proxy.$modal.msgSuccess("添加成功");
    getDeptTree(response.data);
  });
}

function addSon() {
  const childData = {
    children: [],
    id: undefined,
    name: "新的城市",
    fullname: "新的城市",
    code: "",
    parentId:
      initForm.value.levelType == 1
        ? selectNode.value
        : initForm.value.parentId,
    remark: "",
    sort: 0,
    levelType: 2,
  };
  form.value = childData;
  addArea(form.value).then((response) => {
    proxy.$modal.msgSuccess("添加成功");
    getDeptTree(response.data);
  });
  // addChildStatus.value = true

  // console.log(organizationFormPost.value)
}
</script>

<style lang="scss" scoped>
.formStyle1 {
  margin-bottom: 30px;
  max-width: 500px;
}
.contentRow {
  width: 100%;
  height: 100%;
  .contentCol{
    height: 100%;
    display: flex;
    flex-direction: column;

  }
  .treeTitle {
    width: 100%;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .titleTxt {
      flex-shrink: 0;
      flex-grow: 1;
    }
  }
  .head-container{
    // height: 70%;
    height: 0;
    flex-grow: 1;
  }
}
.formWrap {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0;
  width: 100%;

  .formItem1 {
    width: 49%;
  }

  .formItem2 {
    width: 100%;

    .otherInfoList {
      width: 100%;

      .otherInfoItem {
        width: 100%;

        & + .otherInfoItem {
          margin-top: 18px;
        }

        .itemRow {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;

          & + .itemRow {
            margin-top: 18px;
          }

          .labelTxt {
            flex-shrink: 0;
            margin-right: 6px;
            padding: 0;
            color: #999999;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 32px;
          }

          .innerInput {
            margin-right: 20px;
            width: 200px;
          }
        }
      }
    }
    .inputBtnGroup {
      width: 100%;
      display: flex;
      .el-select {
        width: 75%;
        max-width: 360px;
        margin-right: 10px;
      }
    }
  }

  .formCol {
    width: 49%;
  }

  .formItem1,
  .formItem2 {
    &:last-child {
      margin-bottom: 0;
    }

    .normalBtn {
      padding: 0;
      width: 80px;
      height: 32px;
      border-radius: 4px;
      background: #ffffff;

      &:hover,
      &:focus {
        color: var(--el-color-primary);
      }
    }

    .el-form-item__label {
      flex-wrap: wrap;
      margin-right: 6px;
      padding: 0;
      color: #999999;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 32px;

      .labelTxt {
        margin: 0;
        line-height: 18px;
      }
    }

    .btnsRow {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 18px;
    }

    .el-select {
      width: 100%;
    }

    .el-input {
      width: 100%;

      .el-input__wrapper {
        // box-shadow: 0 0 0 1px #d9d9d9 inset;

        .el-input__inner {
          color: #222222;
          font-weight: 400;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;

          &::placeholder {
            color: rgba(0, 0, 0, 0.25);
          }
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        color: #222222;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;

        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .el-cascader {
      width: 100%;

      &.is-disabled {
        .el-input__wrapper {
          background: #ffffff;
        }
      }

      .el-input__inner {
        color: #222222;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;

        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .el-radio-group {
      margin-left: 20px;

      .el-radio__label {
        color: #222222;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 32px;
      }
    }
    .el-checkbox-group {
      margin-left: 20px;
    }

    .uploadWrap1,
    .uploadWrap2 {
      width: 100%;

      .el-upload__tip {
        color: #999999;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 20px;
      }
    }

    .uploadWrap1 {
      .el-upload-list {
        .el-upload {
          display: flex;
          overflow: hidden;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          width: 80px;
          height: 80px;
          border-color: #2f9aea;
          border-radius: 4px;

          .el-icon {
            color: #2f9aea;
            font-weight: 400;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 20px;
          }

          .tip {
            margin: 10px 0 0;
            color: #2f9aea;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 20px;
          }
        }

        .el-upload-list__item {
          overflow: hidden;
          width: 80px;
          height: 80px;
          border-radius: 4px;
        }
      }

      &.uploadOne {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .el-upload__tip {
          display: block;
          margin: 0;
          margin: 0 0 0 8px;
          color: #999999;
          font-weight: 400;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 20px;
        }

        .el-upload-list {
          overflow: hidden;
          width: 80px;
          height: 80px;

          .el-upload-list__item {
            margin: 0;
          }
        }
      }
    }

    .uploadWrap2 {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: flex-start;

      .uploadBtn {
        padding: 0;
        width: 96px;
        height: 36px;
        border-radius: 4px;
        background: #ffffff;

        &:hover,
        &:focus {
          color: var(--el-color-primary);
        }
      }

      .el-upload__tip {
        margin: 0 0 0 8px;
      }

      .el-upload-list {
        width: 60%;
      }
    }

    .formTip {
      margin: 0 0 0 8px;
      color: #999999;
      font-weight: 400;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 20px;
    }

    .formBtn,
    .formBtn2 {
      margin-top: 22px;
      width: 240px;
      height: 32px;
      border: none;
      border-radius: 4px;
      //   background: linear-gradient(90deg, #63baff 0%, #2f9aea 100%);
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .formBtn2 {
      width: 160px;

      &.btnCol {
        background: #ec2a2a;
      }
    }
  }

  &.formStyle1 {
    .formItem1,
    .formItem2 {
      .el-form-item__content {
        flex: initial;
        width: 380px;
      }
    }
  }

  &.formStyle2 {
    margin-bottom: 30px;

    .formItem1,
    .formItem2 {
      .el-form-item__content {
        color: #222222;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
  }

  .formItem2 {
    .toolBar {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      border-top: 1px solid #eeeeee;

      .leftCol {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .tip {
          margin: 0 0 0 8px;
          color: #999999;
          font-weight: 400;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 20px;
        }
      }

      .rightCol {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 240px;
        :deep(.el-input) {
          .el-input__wrapper {
            width: 180px;
          }
        }
        .searchBtn {
          margin-left: 8px;
        }
      }
    }
  }

  .infoDiv {
    display: flex;
    margin-left: 12px;
    .infoIcon {
      margin-left: 10px;
      width: 18px;
      color: #999;
    }
  }
}
</style>
