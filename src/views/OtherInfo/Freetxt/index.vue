<template>
  <div class="app-container">
    <HandleRow v-model:query="queryParams" @search="getList" :placeholder="请输入字样">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </template>
    </HandleRow>

    <!-- 分组 -->
    <CommonTable :listReq="$api.ticketKey.listByPage" :queryParams="queryParams" :tableCols="tableCols1" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig1.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <CommonForm v-model:form="dialogControl.form" :formOptions="formOptions1" :readonly="dialogControl.readonly" ref="formRef" />

      <template #footer v-if="!dialogControl.readonly">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Answer">
import { formConfig1, tableConfig1 } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})

const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols1 = ref(tableConfig1.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions1 = ref(formConfig1.items)

// 重置
function reset() {
  dialogControl.value.form = {}
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
/* 添加 */
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加字样')
}
/* 修改 */
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.ticketKey.detail, row, dialogControl.value, '修改字样')
}
/* 删除 */
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.ticketKey.remove, row)
}
/* 提交 */
function submitForm() {
  proxy.$refs.formRef.formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(proxy.$api.ticketKey.add, proxy.$api.ticketKey.update, dialogControl.value)
    }
  })
}
</script>
