<template>
    <div class="app-container">
        <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
        <HandleRow v-model:query="queryParams" @search="getList" placeholder="广告标题">
            <template #btns>
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
                </el-col>
            </template>
        </HandleRow>
        <CommonTable :listReq="$api.advertisingPicture.listByPage" :queryParams="queryParams" :tableCols="tableCols"
            v-model:multiple="multiple" ref="tableRef">
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
                <template #default="scope">
                    <el-button link type="warning" @click="handleUpdate(scope.row)">编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>

            

        </CommonTable>

        <!-- 添加或修改公告对话框 -->
        <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
        @closed="cancel"
            ref="dialogRef">
            <CommonForm v-model:form = "dialogControl.form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef">
            
                <template #url="{ data }">
                    <el-input v-model="dialogControl.form.url" placeholder="请输入广告链接" size="normal" clearable></el-input>
                    <p class=tips>剧场详情页链接：/pages/sub/detail/theater?id=xx</p>
                    <p class="tips">剧目详情页链接：/pages/sub/detail/repertoire?id=xx</p>
                    <!-- {{ data }} -->
                    
                </template>
            
            </CommonForm>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="cancel">取 消</el-button> -->
                </div>
            </template>
        </el-dialog>
    </div>
</template>
 
<script setup name="Banner">
import { nextTick } from 'vue';
import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance();


/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined
})
const queryOptions = ref(queryConfig.items)
const getList = () => { proxy.$refs.tableRef.getList() }
const multiple = ref(true)
const tableCols = ref(tableConfig.items)


/* 操作和表单配置 */
const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
    dialogControl.value.form = {
        status:1
    };
    // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
    dialogControl.value.open = false;
    dialogControl.value.readonly = false
    reset();
}
function handleAdd() {
    reset()
    proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加广告图')
}
function handleView(row) {
    dialogControl.value.readonly = true
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.advertisingPicture.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.advertisingPicture.detail, row, dialogControl.value, '修改广告图')
}
function handleDelete(row) {
    proxy.$refs.tableRef.handleDelete(proxy.$api.advertisingPicture.remove, row)
}
function submitForm() {
    // console.log(proxy.$refs["formRef"].formRef.validate)
    proxy.$refs["formRef"].formRef.validate(valid => {
        if (valid) {
            proxy.$refs.tableRef.handleSubmit(proxy.$api.advertisingPicture.add, proxy.$api.advertisingPicture.update, dialogControl.value)
        }
    });
}



</script>
<style lang="scss" scoped>
.tips{
    margin: 0;
    padding: 0;
    line-height: 22px;
    font-size: 14px;
    color: var(--el-color-info);
    font-style: oblique;
}


</style>
 