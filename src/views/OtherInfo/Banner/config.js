import { useDict } from '@/utils/dict'

const { status_flag } = useDict('status_flag')

export const tableConfig = {
  items: [
    {
      label: '广告图',
      prop: 'image',
      type: 'Image',
      option: {
        minWidth: 180,
      },
    },
    { label: '标题', prop: 'title', type: 'String', option: {} },
    {
      label: '启用状态',
      prop: 'status',
      type: 'Dict',
      option: {
        pull: status_flag,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'String', option: {} },
  ],
}

export const formConfig = {
  items: [
    {
      label: '广告标题',
      prop: 'title',
      type: 'String',
      option: {},
      rules: [{ required: true, message: '请输入广告标题', trigger: '' }],
      size: 'default',
    },
    {
      label: '广告图',
      prop: 'image',
      type: 'Image',
      option: {
        limit: 1,
      },
      rules: [{ required: true, message: '请上传广告图', trigger: '' }],
      size: 'default',
    },
    {
      label: '广告链接',
      prop: 'url',
      type: 'Slot',
      option: {},
      size: 'default',
    },
    {
      label: '是否启用',
      prop: 'status',
      type: 'Radio',
      option: {
        pull: status_flag,
      },
      size: 'default',
    },
    {
      label: '排序',
      prop: 'sort',
      type: 'Number',
      option: {},
      size: 'default',
    },
  ],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '启动状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: status_flag,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
