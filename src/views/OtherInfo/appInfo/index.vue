<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="appInfoTab">
      <el-tab-pane label="广告图管理" name="first">
        <Banner />
      </el-tab-pane>
      <el-tab-pane label="基本信息设置" name="second">
        <BasicInfo />
      </el-tab-pane>
      <el-tab-pane label="用户协议设置" name="third">
        <Protocol />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="appInfo">
import Banner from '../Banner/index.vue'
import BasicInfo from '../BasicInfo/index.vue'
import Protocol from '../Protocol/index.vue'
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const activeName = ref('first')
</script>

<style lang="scss" scoped>
.appInfoTab {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .app-container {
    padding: 0;
    height: 100%;
  }
  &:deep {
    .el-tabs__content {
      height: 100%;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>
