
import { useDict } from '@/utils/dict'

const { agreement_type, status_flag } = useDict("agreement_type", "status_flag")


export const tableConfig = {
    items: [
        { label: '协议类型', prop: 'type', type: 'Dict', option: {
            pull:agreement_type
        } },
        // { label: '所属端口', prop: 'port', type: 'String', option: {} },
        { label: '启用状态', prop: 'status', type: 'Switch', option: {
           
        } }
    ]

}
export const formConfig = {
    items: [
        { label: '协议类型', prop: 'type', type: 'Radio', option: {
            pull:agreement_type
        }, size:'default' },
    
        { label: '协议内容', prop: 'body', type: 'Editor', option: {

        }, size:'default' },
        { label: '是否启用', prop: 'status', type: 'Radio', option: {
            pull:status_flag
        }, size:'default' }

    ],
    size: 'default'
}

export const queryConfig = {
    items: [
    ]
}