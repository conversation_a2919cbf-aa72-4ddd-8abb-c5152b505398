<template>
    <div class="pushTimeBox">
        <el-date-picker
        class="date"
            v-model="date"
            type="date"
            placeholder="发送日期"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            @change="dateChange"
            :disabled-date="startTimeDisabled"
            :disabled="disabled"
        />
        <div class="separator"> - </div>
        <el-time-select
        class="time"
            v-model="time"
            step="00:30:00"
            start="00:00:00"
            end="24:00:00"
            placeholder="发送时间"
            format="HH:mm"
            @change="timeChange"
            :disabled="disabled"
            
        />
    </div>
</template>

<script setup name="pushTime">
import moment from 'moment';
const emits = defineEmits()
const props = defineProps({
    modelValue:{
        type:String,
        default:undefined
    },
    disabled:{
        type:Boolean,
        default:false
    }
})


const date = ref(moment().format('YYYY-MM-DD'))
const time = ref('00:00')

watch(()=>props.modelValue, val=> {
    if(val){
        const dateTime = props.modelValue.split(' ')
        date.value = dateTime[0]
        time.value = dateTime[1]
    }
    else{
        date.value = ''
        time.value = ''
    }
},{immediate:true})

function getValue(){
    return date.value + ' ' + time.value
}

function dateChange(data){
    if(!time.value) time.value = '00:00'
    emits('update:modelValue', getValue())
}
function timeChange(data){
    // console.log(data)
    emits('update:modelValue', getValue())
}
function startTimeDisabled(date){
    return moment(date).add(1,'days').isBefore(moment())
}
</script>


<style lang="scss" scoped>

.pushTimeBox{
    display: flex;
    width: 100%;
    &:deep{
        .date{
          
            max-width: 140px;
        }
        .time{
            max-width: 130px;
        }
        .separator{
            margin: 0 8px;
        }
    }
    

}
</style>