<template>
  <div class="app-container">
    <SearchForm
      v-model:open="queryOpen"
      :options="queryOptions"
      v-model:query="queryParams"
      @search="getList"
    />
    <HandleRow
      v-model:query="queryParams"
      @search="getList"
      placeholder="公告标题"
    >
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info"
            >高级筛选</el-button
          >
        </el-col>
      </template>
    </HandleRow>
    <CommonTable
      :listReq="$api.notify.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols"
      :formatSubForm="formatSubForm"
      :formatGetForm="formatGetForm"
      v-model:multiple="multiple"
      ref="tableRef"
    >
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="140"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="success"
            @click="handleSend(scope.row)"
            :disabled="scope.row.status"
            >立即推送</el-button
          >
          <el-button
            link
            type="info"
            @click="handleCancel(scope.row)"
            :disabled="scope.row.status"
            >取消</el-button
          >
          <el-button
            link
            type="warning"
            @click="handleUpdate(scope.row)"
            :disabled="scope.row.status"
            >修改</el-button
          >
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            :disabled="scope.row.status"
            >删除</el-button
          >
        </template>
      </el-table-column>

      <template #body="{ data }">
        <el-button type="primary" link @click="handleView(data.scope.row)">{{
          "查看详情"
        }}</el-button>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >
      <CommonForm
        v-model:form="dialogControl.form"
        :formOptions="formOptions"
        :readonly="dialogControl.readonly"
        ref="formRef"
      >
        <template #pushTime>
          <PushTime
            v-model:modelValue="dialogControl.form.timedReleaseTime"
            :disabled="pushTimeDisabled"
            v-if="!dialogControl.readonly"
          />
          <span v-else>
            {{
              dialogControl.form.pushPassTime ||
              dialogControl.form.timedReleaseTime
            }}
          </span>
        </template>
      </CommonForm>
      <template #footer v-if="!dialogControl.readonly">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import { nextTick } from "vue";
import PushTime from "./detail/PushTime.vue";
import { tableConfig, formConfig, queryConfig } from "./config";
import moment from "moment";
// import moment from 'moment';
const { proxy } = getCurrentInstance();

/* 查询和表格配置 */
const queryOpen = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
});
const queryOptions = ref(queryConfig.items);
const getList = () => {
  proxy.$refs.tableRef.getList();
};
const multiple = ref(true);
const tableCols = ref(tableConfig.items);

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  form: {},
  readonly: false,
});
const formOptions = ref(formConfig.items);

/* 控制推送时间 */
const pushTimeDisabled = ref(false);
watch(
  () => dialogControl.value.form.pushType,
  (val) => {
    if (!dialogControl.value.readonly) {
      if (val == 1) {
        dialogControl.value.form.timedReleaseTime = "";
        pushTimeDisabled.value = true;
      } else if (val == 2) {
        dialogControl.value.form.timedReleaseTime = "";
        pushTimeDisabled.value = true;
      } else {
        dialogControl.value.form.timedReleaseTime =
          dialogControl.value.form.timedReleaseTime ||
          moment().format("YYYY-MM-DD") + " " + "00:00";
        pushTimeDisabled.value = false;
      }
    }
  }
);

// 重置
function reset() {
  dialogControl.value.form = {
    pushType: 1,
  };
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false;
  dialogControl.value.readonly = false;
  reset();
}
function handleAdd() {
  reset();
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, "添加通知");
}
function handleView(row) {
  // console.log(row)
  dialogControl.value.readonly = true;
  proxy.$refs.tableRef.handleGetDetail(
    proxy.$api.notify.detail,
    row,
    dialogControl.value,
    "查看详情"
  );
}
function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(
    proxy.$api.notify.detail,
    row,
    dialogControl.value,
    "修改通知"
  );
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.notify.remove, row);
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs["formRef"].formRef.validate((valid) => {
    if (valid) {
      const time = dialogControl.value.form.timedReleaseTime;
      if (moment(time).isBefore(moment())) {
        proxy.$modal.msgError("发布时间不可为过去时");
        return;
      }
      // proxy.$modal.msgSuccess('fabus')
      proxy.$refs.tableRef.handleSubmit(
        proxy.$api.notify.add,
        proxy.$api.notify.update,
        dialogControl.value
      );
    }
  });
}

function formatSubForm(form) {
  const postForm = proxy.deepClone(form);

  if (postForm.port.length == 1) {
    postForm.port = postForm.port[0];
  } else if (postForm.port.length == 2) {
    postForm.port = postForm.port[0] + postForm.port[1];
  }
  if (postForm.pushType == 1) {
    postForm.status = 1;
  } else {
    postForm.status = 0;
  }
  if (postForm.timedReleaseTime) {
    postForm.timedReleaseTime = moment(postForm.timedReleaseTime).format(
      "YYYY-MM-DD HH:mm:ss"
    );
  }

  if (postForm.body) {
    // if (postForm.body?.replaceAll)
    //   postForm.body = postForm.body.replaceAll("\n", "<br/>");
    if (postForm.body?.replace)
      postForm.body = postForm.body.replace(/\n/g, "<br/>");

    // console.log("==================", postForm.body);
  }
  return postForm;
}
function formatGetForm(form) {
  form.port = getValues(form.port);
  form.timedReleaseTime = moment(form.timedReleaseTime).format(
    "YYYY-MM-DD HH:mm"
  );
  // console.log('form',form)
  if (form.body) {
    if (!dialogControl.value.readonly) {
      //   console.log("==========", form.body);
      if (form.body?.replace) form.body = form.body.replace(/<br\/>/g, "\n");
      //   console.log("==================", form.body);
    }
  }

  return form;
}

function getValues(val) {
  if (val == 3) {
    return [1, 2];
  } else if (val == 2) {
    return [2];
  } else if (val == 1) {
    return [1];
  }
}

function handleSend(row) {
  proxy.$api.notify
    .update({
      id: row.id,
      status: 1,
    })
    .then((res) => {
      proxy.$modal.msgSuccess("发布成功");
      getList();
    });
}
</script>
