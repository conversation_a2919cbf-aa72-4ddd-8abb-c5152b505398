import { useDict } from '@/utils/dict'
const { push_status } = useDict('push_status')

export const tableConfig = {
  items: [
    // { label: '编号', prop: 'no', type: 'String', option: {} },
    { label: '公告标题', prop: 'title', type: 'String', option: {} },
    {
      label: '公告详情',
      prop: 'body',
      type: 'Slot',
      option: {
        minWidth: '100',
      },
    },
    {
      label: '发布类型',
      prop: 'pushType',
      type: 'Select',
      option: {
        pull: [
          { value: 1, label: '立即发布' },
          { value: 2, label: '手动发布' },
          { value: 3, label: '定时发布' },
        ],
      },
    },
    {
      label: '发布对象',
      prop: 'port',
      type: 'Select',
      option: {
        pull: [
          { value: 1, label: '商家端' },
          { value: 2, label: '用户端' },
          { value: 3, label: '商家端、用户端' },
        ],
        minWidth: 200,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '定时发布时间',
      prop: 'timedReleaseTime',
      type: 'Time',
      option: {},
    },
    {
      label: '发布完成时间',
      prop: 'pushPassTime',
      type: 'Time',
      option: {},
    },
    {
      label: '发布状态',
      prop: 'status',
      type: 'Dict',
      option: {
        pull: push_status,
      },
    },
  ],
}

export const formConfig = {
  items: [
    { label: '公告标题', prop: 'title', type: 'String', option: {}, rules: [{ required: true, message: '请输入公告标题', trigger: '' }], size: 'default' },
    {
      label: '公告详情',
      prop: 'body',
      type: 'String',
      option: {
        type: 'textarea',
        rows: 5,
      },
      rules: [{ required: true, message: '请输入公告详情', trigger: '' }],
      size: 'default',
    },
    {
      label: '发布对象',
      prop: 'port',
      type: 'Checks',
      option: {
        pull: [
          { value: 1, label: '商家端' },
          { value: 2, label: '用户端' },
        ],
      },
      size: 'default',
    },
    {
      label: '发布类型',
      prop: 'pushType',
      type: 'Radio',
      option: {
        pull: [
          { value: 1, label: '立即发布' },
          { value: 2, label: '手动发布' },
          { value: 3, label: '定时发布' },
        ],
      },
      size: 'default',
    },
    {
      label: '发布时间',
      prop: 'pushTime',
      type: 'Slot',
      option: {},

      size: 'default',
    },
  ],
  size: 'default',
}

export const queryConfig = {
  items: [
    {
      label: '发布类型',
      prop: 'pushType',
      type: 'tags',
      option: {
        pull: [
          { value: 1, label: '立即发布' },
          { value: 2, label: '手动发布' },
          { value: 3, label: '定时发布' },
        ],
      },
    },
    {
      label: '发布对象',
      prop: 'port',
      type: 'tags',
      option: {
        pull: [
          { value: 1, label: '商家端' },
          { value: 2, label: '用户端' },
        ],
      },
    },
    {
      label: '发布状态',
      prop: 'status',
      type: 'tags',
      option: {
        pull: push_status,
      },
    },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
