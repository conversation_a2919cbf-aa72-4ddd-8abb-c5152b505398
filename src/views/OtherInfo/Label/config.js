
import { useDict } from '@/utils/dict'


export const tableConfig = {
    items: [
        { label: '标签名称', prop: 'name', type: 'String', option: {} },
        { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
        // { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
        { label: '创建时间', prop: 'createTime', type: 'String', option: {} }
    ]

}
export const formConfig = {
    items: [
        { label: '标签名称', prop: 'name', type: 'String', option: {}, size:'default' },
    ],
    size: 'small'
}

export const queryConfig = {
    items: [
        {
            label: '创建时间', prop: 'time', type: 'daterange', option: {
            }
        },
    ]
}