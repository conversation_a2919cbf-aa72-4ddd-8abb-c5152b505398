export const tableConfig = {
  items: [
    { label: '用户名称', prop: 'name', type: 'String', option: {} },
    { label: '手机号码', prop: 'phone', type: 'String', option: {} },
    { label: '手机型号', prop: 'deviceModel', type: 'String', option: {} },
    { label: 'IP地址', prop: 'ipAddress', type: 'String', option: {} },
    { label: '扫描图片', prop: 'fileUrl', type: 'Image', option: { minWidth: 200 } },
    { label: '扫票时间', prop: 'createTime', type: 'String', option: {} },
    { label: '扫票状态', prop: 'portfolioId', type: 'Slot', option: {} },
    { label: '领取状态', prop: 'getCount', type: 'Slot', option: {} },
  ],
}

export const formConfig = {
  items: [
    { label: '用户名称', prop: 'name', type: 'String', option: {}, size: 'default' },
    { label: '手机号码', prop: 'phone', type: 'String', option: {}, size: 'default' },
    { label: '手机型号', prop: 'deviceModel', type: 'String', option: {}, size: 'default' },
    { label: 'IP地址', prop: 'ipAddress', type: 'String', option: {}, size: 'default' },
    { label: '扫票时间', prop: 'createTime', type: 'String', option: {}, size: 'default' },
    { label: '扫票状态', prop: 'portfolioId', type: 'Slot', option: {}, size: 'default' },
    { label: '领取状态', prop: 'getCount', type: 'Slot', option: {}, size: 'default' },
    { label: '扫描图片', prop: 'fileUrl', type: 'Slot', option: { limit: 1 }, size: 'default' },
    { label: '扫票结果', prop: 'body', type: 'Slot', option: {}, size: 'default' },
    { label: '结果详情', prop: 'wordList', type: 'Slot', option: {}, size: 'default' },
  ],
  size: 'small',
}

export const queryConfig = {
  items: [{ label: '扫票时间', prop: 'time', type: 'daterange', option: {} }],
}
