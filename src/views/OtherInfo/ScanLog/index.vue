<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户名称">
      <!-- <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template> -->
    </HandleRow>

    <CommonTable :listReq="$api.scan.list" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>
        </template>
      </el-table-column>

      <template #portfolioId="{ data }">
        <div v-if="data.scope.row.portfolioId" style="color: var(--el-color-success)">成功</div>
        <div v-else style="color: var(--el-color-danger)">失败</div>
      </template>
      <template #getCount="{ data }">
        <div v-if="data.scope.row.getCount" style="color: var(--el-color-success)">已领取</div>
        <div v-else style="color: var(--el-color-danger)">未领取</div>
      </template>
    </CommonTable>

    <!-- 详情对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <CommonForm v-model:form="dialogControl.form" :formOptions="formOptions" :readonly="dialogControl.readonly" ref="formRef">
        <template #portfolioId="{ data }">
          <div v-if="data.form.portfolioId" style="color: var(--el-color-success)">成功</div>
          <div v-else style="color: var(--el-color-danger)">失败</div>
        </template>

        <template #getCount="{ data }">
          <div v-if="data.form.getCount" style="color: var(--el-color-success)">已领取</div>
          <div v-else style="color: var(--el-color-danger)">未领取</div>
        </template>

        <template #fileUrl="{ data }">
          <ImagePreview style="margin: 0" :src="baseUrl + data.form.fileUrl" :width="100" :height="100" :picNumMax="1" />
        </template>

        <template #body="{ data }">
          <JsonViewer :value="scanRes" boxed copyable sort theme="light" expand-depth="2" style="width: 100%" />
        </template>

        <template #wordList="{ data }">
          <JsonViewer :value="scanResDetail" boxed copyable sort theme="light" expand-depth="2" style="width: 100%" />
        </template>
      </CommonForm>
    </el-dialog>
  </div>
</template>

<script setup name="Theater">
import { formConfig, queryConfig, tableConfig } from './config'
import _ from 'lodash'

const baseUrl = import.meta.env.VITE_APP_BASE_API
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

const scanRes = computed(() => {
  let temp

  if (dialogControl.value.form.body) temp = _.cloneDeep(JSON.parse(dialogControl.value.form.body))
  else temp = dialogControl.value.form.body

  return temp
})

const scanResDetail = computed(() => {
  let temp

  if (dialogControl.value.form.wordList) temp = _.cloneDeep(JSON.parse(dialogControl.value.form.wordList))
  else temp = dialogControl.value.form.wordList

  return temp
})

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = { name: '' }
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}

function handleView(row) {
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.scan.detail, row, dialogControl.value, '查看详情')
}
</script>
