<template>
  <div class="app-container">
    <el-scrollbar class="containerScroll">
      <div class="BasicInfo">
        <el-form :model="form" ref="formRef" :rules="rules" label-width="7em" :inline="false" size="normal">
          <el-form-item label="名称">
            <el-input v-model="form.wechatName" style="max-width: 320px"></el-input>
          </el-form-item>

          <el-form-item label="上传logo图片">
            <ImageUpload v-model:modelValue="form.wechatLogo" :limit="1" />
          </el-form-item>

          <el-form-item label="添加平台免责声明" class="labelTop">
            <div class="states">
              <div class="stateRow">
                <div class="stateLabel">普通票</div>
                <el-input v-model="statement1" type="textarea" :rows="6" placeholder="请输入平台普通票免责声明内容" size="normal" clearable></el-input>
              </div>

              <div class="stateRow">
                <div class="stateLabel">升级票</div>
                <el-input v-model="statement2" type="textarea" :rows="6" placeholder="请输入平台普通票免责声明内容" size="normal" clearable></el-input>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="">
            <el-button type="primary" size="default" class="btn" @click="submit">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup name="BasicInfo">
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const form = ref({})

const statement1 = ref()
const statement2 = ref()
const detail = ref()

function submit() {
  proxy.$api.wechatSetting.update(form.value).then((res) => {
    proxy.$modal.msgSuccess('修改成功')
    getDetail()
  })

  detail.value.map((i) => {
    if (i.type === 1) i.statement = statement1.value
    if (i.type === 2) i.statement = statement2.value
  })

  proxy.$api.portfolioStatement.update(detail.value).then((res) => {
    // proxy.$modal.msgSuccess('修改成功')
    getDetail2()
  })
}

function getDetail() {
  proxy.$api.wechatSetting.detail().then((res) => {
    form.value = res.data
  })
}

function getDetail2() {
  proxy.$api.portfolioStatement
    .detail({
      pageNum: 1,
      pageSize: 1000,
    })
    .then((res) => {
      detail.value = res.data.rows
      res.data.rows.map((i) => {
        if (i.type === 1) statement1.value = i.statement
        if (i.type === 2) statement2.value = i.statement
      })
    })
}

onMounted(() => {
  getDetail()
  getDetail2()
})
</script>

<style lang="scss" scoped>
.containerScroll {
  width: 100%;
}
.BasicInfo {
  padding: 20px;
  max-width: 620px;
  .btn {
    width: 190px;
  }
  .labelTop {
    display: block;
    &:deep(.el-form-item__label) {
      width: 100% !important;
      text-align: left;
      justify-content: flex-start;
    }
    .states {
      width: 100%;
      padding-left: 3em;
      .stateRow {
        display: flex;
        margin-top: 20px;
        .stateLabel {
          flex-shrink: 0;
          margin-right: 12px;
        }
      }
    }
  }
}
</style>
