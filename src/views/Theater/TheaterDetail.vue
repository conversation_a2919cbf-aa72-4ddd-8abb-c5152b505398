<template>
  <div class="intro">
    <div class="cover">
      <div class="img">
        <!-- <el-image :src="form.cover.imgList[form.cover.pageNum - 1]" fit="contain" style="width:100%;height:100%"></el-image> -->
        <el-carousel height="240px" :loop="false" :autoplay="false" arrow="never" indicator-position="none" ref="carouselRef">
          <el-carousel-item v-for="(item, index) in form.cover.imgList" :key="index">
            <div class="tag" v-if="index == 0">封面</div>
            <el-image :src="item" preview-teleported :preview-src-list="[item]" fit="cover" style="width: 100%; height: 100%"></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="control">
        <div class="pagination">
          <el-button class="button" size="mini" @click="imgPrev()" :disabled="form.cover.pageNum === 1">上一页</el-button>
          <span class="page-text">
            {{ form.cover.pageNum + ' / ' + form.cover.pageSize }}
          </span>
          <el-button class="button" size="mini" @click="imgNext()" :disabled="form.cover.pageNum === form.cover.pageSize">下一页</el-button>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="name-section">
        <div class="name" @dblclick="openEditDialog">
          {{ form.name }}
          <div @click="openEditDialog" class="edit-btn">
            <el-icon>
              <Edit />
            </el-icon>
          </div>
        </div>
      </div>
      <MarkList :count="form.markList" />
    </div>
  </div>
  <div class="table-list">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="关联剧目" name="first">
        <PlayList :theater-id="props.form.id" />
      </el-tab-pane>
      <el-tab-pane label="纪念徽章" name="second">
        <Emblem :theater-id="props.form.id" :disable-search="true" />
      </el-tab-pane>
      <el-tab-pane label="等级勋章" name="third">
        <Medal :theater-id="props.form.id" :disable-search="true" />
      </el-tab-pane>
      <el-tab-pane label="问大家" name="fourth">
        <AskList :theater-id="props.form.id" merchant-category="2" />
      </el-tab-pane>
      <el-tab-pane label="剧场动态" name="five">
        <Dynamic :theater-id="props.form.id" :disable-search="true" />
      </el-tab-pane>
    </el-tabs>
  </div>

  <!-- 编辑名称对话框 -->
  <el-dialog v-model="editDialogVisible" width="620px" :close-on-click-modal="false" @close="cancelNameEdit" class="edit-name-dialog" align-center append-to-body :show-close="false">
    <template #header>
      <div class="dialog-header">
        <div class="header-icon">
          <el-icon size="20">
            <Edit />
          </el-icon>
        </div>
        <div class="header-content">
          <h3 class="dialog-title">编辑剧院名称</h3>
          <p class="dialog-subtitle">修改剧院的显示名称</p>
        </div>
      </div>
    </template>

    <div class="dialog-body">
      <div class="form-container">
        <el-form :model="editForm" :rules="editRules" ref="editFormRef" class="edit-form">
          <el-form-item prop="name" class="name-input-item">
            <el-input v-model="editForm.name" placeholder="请输入剧院名称" maxlength="50" show-word-limit @keyup.enter="saveNameEdit" size="large" class="name-input" />
          </el-form-item>
        </el-form>
        <div class="input-tip">
          <el-icon>
            <InfoFilled />
          </el-icon>
          <span>名称长度限制在1-50个字符之间</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="large" @click="cancelNameEdit" class="cancel-btn">
          <el-icon>
            <Close />
          </el-icon>
          取消
        </el-button>
        <el-button type="primary" size="large" @click="saveNameEdit" :loading="saving" class="save-btn">
          <el-icon v-if="!saving">
            <Check />
          </el-icon>
          {{ saving ? '保存中...' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="theaterDetail">
import img2 from '@/assets/images/avatar1.jpg'
import img1 from '@/assets/images/profile.jpg'
import { Check, Close, Edit, InfoFilled } from '@element-plus/icons-vue'
import Emblem from '../Assets/Emblem/index.vue'
import Dynamic from '../Dynamic/index.vue'
import Medal from '../Medal/index.vue'
import AskList from './detail/askList.vue'
import MarkList from './detail/markList.vue'
import PlayList from './detail/playList.vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['nameUpdated'])
const form = ref({
  name: '上海大剧院',
  cover: {
    imgList: [img1, img2],
    pageNum: 1,
    pageSize: 2,
  },
  markList: [0, 0, 0],
})

// 编辑名称相关状态
const editDialogVisible = ref(false)
const saving = ref(false)
const editFormRef = ref(null)
const editForm = ref({
  name: ''
})
const editRules = {
  name: [
    { required: true, message: '请输入剧院名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  form.value.name = props.form.name
  let imgArr = []
  if (props.form.pictures) {
    imgArr = [props.form.coverPicture, ...props.form.pictures.split(',')]
  } else {
    imgArr = [props.form.coverPicture]
  }

  form.value.cover.imgList = imgArr.map((i) => {
    return proxy.addBaseUrl(i)
  })
  form.value.cover.pageSize = imgArr.length
  form.value.cover.pageNum = 1

  form.value.markList = [props.form.focusNumber, props.form.commentCount, props.form.interactionCount]
})

function imgPrev() {
  proxy.$refs['carouselRef'].prev()
  form.value.cover.pageNum--
}
function imgNext() {
  proxy.$refs['carouselRef'].next()
  form.value.cover.pageNum++
}

// 编辑名称相关方法
function openEditDialog() {
  editForm.value.name = form.value.name
  editDialogVisible.value = true
}

function cancelNameEdit() {
  editDialogVisible.value = false
  editForm.value.name = ''
  saving.value = false
  // 重置表单验证
  nextTick(() => {
    editFormRef.value?.clearValidate()
  })
}

async function saveNameEdit() {
  // 表单验证
  const valid = await editFormRef.value?.validate().catch(() => false)
  if (!valid) return

  if (editForm.value.name.trim() === form.value.name) {
    cancelNameEdit()
    return
  }

  saving.value = true
  try {
    await proxy.$api.theater.update({
      id: props.form.id,
      name: editForm.value.name.trim()
    }, {
      headers: { repeatSubmit: false }
    })

    form.value.name = editForm.value.name.trim()
    proxy.$modal.msgSuccess('剧院名称修改成功')
    cancelNameEdit()

    // 触发父组件更新
    emit('nameUpdated', {
      id: props.form.id,
      name: editForm.value.name.trim()
    })
  } catch (error) {
    console.error('修改剧院名称失败:', error)
    if (error.message !== '数据正在处理，请勿重复提交') {
      proxy.$modal.msgError('修改失败，请重试')
    }
  } finally {
    saving.value = false
  }
}

const activeName = 'first'
</script>

<style lang="scss" scoped>
.intro {
  width: 100%;
  display: flex;
  .cover {
    width: 420px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    .img {
      width: 100%;
      height: 240px;
      flex-shrink: 0;
      position: relative;
      .tag {
        width: 42px;
        height: 25px;
        border-radius: 4px;
        background-color: rgba(253, 187, 44, 0.8);
        color: rgba(255, 255, 255, 1);
        font-size: 14px;
        text-align: center;
        font-family: Microsoft Yahei;
        line-height: 25px;
        position: absolute;
        right: 16px;
        top: 8px;
        z-index: 100;
      }
    }
    .control {
      width: 100%;
      height: 50px;
      padding: 9px 0;
      text-align: center;
      .pagination {
        .button {
          height: 32px;
          padding: 0 10px;
        }
        .page-text {
          margin: 0 20px;
        }
      }
    }
  }
  .info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    // height: 100%;
    height: 240px;
    padding: 0 20px;
    .name-section {
      flex-grow: 1;
      .name {
        color: rgba(16, 16, 16, 1);
        font-size: 20px;
        text-align: left;
        font-family: SourceHanSansSC-medium;
        display: flex;
        align-items: center;
        cursor: pointer;
        .edit-btn {
          margin-left: 8px;
          opacity: 1; // 常显示编辑按钮
          transition: opacity 0.3s;
          color: #409eff;
        }
      }
    }
  }
}
.table-list {
  margin-top: 16px;
  max-height: calc(88vh - 410px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .el-tabs {
    // height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
    :deep(.el-tabs__content) {
      flex-grow: 1;
      overflow: auto;
      .app-container {
        padding: 0;
      }
    }
  }
}

// 编辑名称弹框样式
:deep(.edit-name-dialog) {
  .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
  }

  .header-content {
    flex: 1;

    .dialog-title {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      line-height: 1.2;
    }

    .dialog-subtitle {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.2;
    }
  }
}

.dialog-body {
  padding: 24px 10px;
  display: flex;
  justify-content: center;

  .form-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .input-label {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      text-align: center;
      width: 100%;
    }

    .edit-form {
      width: 100%;

      .name-input-item {
        margin-bottom: 0;

        :deep(.el-form-item__content) {
          justify-content: center;
        }

        .name-input {
          width: 100%;

          :deep(.el-input__wrapper) {
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #d9d9d9;
            padding: 12px 16px;

            &:hover {
              box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
              transform: translateY(-1px);
            }

            &.is-focus {
              box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
              transform: translateY(-1px);
            }
          }

          :deep(.el-input__inner) {
            text-align: center;
            font-size: 16px;
            font-weight: 500;
          }

          :deep(.el-input__count) {
            background: transparent;
          }
        }
      }
    }

    .input-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      font-size: 13px;
      color: #666;
      text-align: center;

      .el-icon {
        margin-right: 6px;
        color: #409eff;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-around;
  gap: 12px;

  .cancel-btn {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    color: #666;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  .save-btn {
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    min-width: 120px;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    &.is-loading {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
}
</style>
