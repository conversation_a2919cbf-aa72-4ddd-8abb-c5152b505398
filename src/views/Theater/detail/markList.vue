<template>
    <div class="mark-list">
        <div class="mark" v-for="(item,index) in markList" :key="index" :title="item.name">
            <svg-icon :class-name="item.svg" :icon-class="item.svg" />
            <div>{{ item.val }}</div>
        </div>
    </div>
</template>


<script setup name="MarkList">
const props = defineProps({
    count:{
        type:Object,
        default:()=>([0,0,0 ])
    }
})

const markList = computed(()=> {
    return [
        {
            name:'喜爱',
            svg:'love',
            val:props.count[0],
        },
        {
            name:'评论',
            svg:'comment',
            val:props.count[1],
        },
        {
            name:'互动',
            svg:'active',
            val:props.count[2],
        },
    ]
})


</script>

<style lang="scss" scoped>

.mark-list{
            height: 30px;
            padding: 5px 0;
            display: flex;
            align-items: center;
            width:100%;
            max-width: 300px;
            .mark{
                flex: 1;
                font-size: 20px;
                width: 80px;
                display: flex;
                justify-content: center;
                div{
                    text-align: left;
                    font-size: 14px;
                    font-family: SourceHanSansSC-regular;
                    line-height: 20px;
                    padding: 0 4px;
                }
            }
        }
    
</style>