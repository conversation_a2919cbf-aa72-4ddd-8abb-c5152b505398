<template>
  <div class="list" v-if="list.length">
    <el-card class="list-item" shadow="hover" v-for="(item, index) in list" :key="index">
      <div class="index">{{ index + 1 }}</div>
      <PlayInfo class="playInfo" :form="item" />
      <div class="handleBtn">
        <el-button type="primary" plain size="default" @click="handleView(item)">剧目详情</el-button>
      </div>
    </el-card>
  </div>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

  <!-- 添加或修改公告对话框 -->
  <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="1200" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
    <!-- <CommonForm v-model:form = "form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef"/> -->
    <PlayDetail :form="dialogControl.form" @nameUpdated="handleNameUpdated" />
  </el-dialog>
</template>

<script setup name="playList">
import { computed, getCurrentInstance, onMounted } from "vue";
import PlayDetail from "../../Play/PlayDetail.vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  theaterId: {
    type: String,
    default: undefined
  }
})

const list = ref([]);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  theaterId: (props.theaterId + '') || undefined
});
const total = computed(() => {
  return list.value.length;
});

const getList = () => {
  proxy.$api.repertoire.listByPage(queryParams.value).then((res) => {
    list.value = res.data.rows;
  });
};
onMounted(() => {
  getList();
});


/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false
})
function handleView(row) {
  proxy.$api.repertoire.findRepertoireInfo({ id: row.id }).then(res => {
    dialogControl.value.form = res.data
    dialogControl.value.title = '剧目详情'
    dialogControl.value.open = true
  })
}

function handleNameUpdated(data) {
  // 更新对话框中的数据
  dialogControl.value.form.name = data.name
  // 刷新列表数据
  getList()
}

function cancel() {
  dialogControl.value.open = false
}
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  :deep(.list-item) {
    margin-bottom: 13px;
    .el-card__body {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 20px 50px !important;

      .index {
        margin-right: 60px;
      }
      .playInfo {
        width: 0;
        flex-grow: 1;
      }
      .handleBtn {
        // width:90px;
      }
    }
  }
}
</style>
