<template>
    <CommonTable :listReq="$api.issue.listByPage" :queryParams="queryParams" :tableCols="tableCols" :hasSelect="false" @forExpand="handleExpand"
            ref="tableRef">
            <el-table-column type="expand" label="展开 / 收起" min-width="120">
                <template #default="props">
                    <div class="askListBox">
                        <div class="askList">
                            <el-row class="askItem" :gutter="20" v-for="(item,index) in props.row.userList" :key="item.id">
                                <el-col :span="2">
                                    <div class="index">{{ index+1 }}</div>
                                </el-col>
                                <el-col :span="2">
                                    <DefaultAvatar :url="baseUrl + (item.replyAvatar || item.merchantReplyAvatar)" />
                                </el-col>
                                <el-col :span="20">
                                    <div class="content">
                                        <p class="name">
                                            {{ item.replyName }}
                                        </p>
                                        <p class="content">
                                            {{ item.content || '-' }}
                                        </p>
                                        <p class="time">
                                            {{ item.createTime }}
                                        </p>
                                    </div>
                                </el-col>
                            </el-row>
                        
                        </div>
                    </div>
                </template>
            </el-table-column>

            <template #replyCount = "{data}">
                <div v-if="data.scope.row.replyCount">
                    {{ '有' + data.scope.row.replyCount + '个人回复' }}
                </div>
                <div v-else>
                    暂无回复
                </div>
            </template>
        </CommonTable>
</template>

<script setup name="askList">
import { nextTick, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const baseUrl = import.meta.env.VITE_APP_BASE_API
const props = defineProps({
    theaterId:{
        type:Number,
        default:undefined
    },
    repertoireId:{
        type:Number,
        default:undefined
    },
    merchantCategory:{
        type: String,
        default:'1'
    }
})

const tableConfig = {
    items: [
        { label: '用户头像', prop: 'userAvatar', type: 'Avatar', option: {
            minWidth:120
        } },
        { label: '用户昵称', prop: 'userName', type: 'String', option: {} },
        { label: '问答内容', prop: 'content', type: 'String', option: {
            minWidth:300, showTip:true
        } },
        { label: '提问时间', prop: 'createTime', type: 'Time', option: {} },
        { label: '回复状态', prop: 'replyCount', type: 'Slot', option: {} },
    ]

}


/* 查询和表格配置 */
const tableCols = ref(tableConfig.items)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    merchantCategory: props.merchantCategory || '1',
    theaterId: props.theaterId || undefined,
    repertoireId: props.repertoireId || undefined,
})
const getList = () => { proxy.$refs.tableRef.getList() }

function handleExpand(rows){
    // console.log(expandedRows)
    if(rows.length){
        rows.forEach(row => {
            proxy.$api.issue.detail({id: row.id}).then(res => {
                row.userList = res.data.userList
            })
        })
    }
   
}

onMounted(()=> {
    console.log()
})
</script>

<style lang="scss" scoped>
.askList{
    padding: 10px 180px;
    .askItem{
        margin-bottom: 17px;
        &:last-child{
            margin-bottom: 0;
        }
        .index{
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .content{
            p{
                margin: 0;
                line-height: 20px;
            }
        }
    }
}

</style>