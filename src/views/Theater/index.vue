<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧场名称">
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>
    <CommonTable :listReq="$api.theater.listByPage" :queryParams="queryParams" :tableCols="tableCols" :statusReq="$api.theater.updateStatus" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="150" fixed="right">
        <template #default="scope">
          <!-- <el-button link type="success" @click="handleCheck(scope.row)">审核</el-button> -->

          <div>
            <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </div>
          <div>
            <BtnForCheck :req="$api.theater.audit" :disabled="scope.row.audit" :rowId="scope.row.id" hasPassEvent @pass="handlePass(scope.row)" @change="getList" />
            <el-button link type="warning" @click="handleRecommend(scope.row, scope.row.recommend == 0 ? '推荐成功' : '推荐已取消')">{{
              scope.row.recommend == 0 ? '推荐' : '已推荐'
            }}</el-button>
          </div>
        </template>
      </el-table-column>

      <template #name="{ data }">
        <div>
          {{ data.scope.row.name }}
        </div>
        <!-- <div class="shortName">{{ '短标题' }}</div> -->
        <div class="shortName" v-if="data.scope.row.shortName">{{ data.scope.row.shortName }}</div>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="1200" append-to-body destroy-on-close draggable :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
      <!-- <CommonForm v-model:form = "form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef"/> -->
      <TheaterDetail :form="dialogControl.form" @nameUpdated="handleNameUpdated" />
    </el-dialog>

    <!-- 添加或修改公告对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl2.title" v-model="dialogControl2.open" :width="480" append-to-body destroy-on-close :close-on-click-modal="false" draggable @closed="cancel" ref="dialogRef">
      <el-form :model="dialogControl2.form" label-width="7em" ref="formRef" label-position="top" class="auditForm">
        <div class="title">{{ dialogControl2.form.name }}</div>
        <el-form-item label="" size="normal">
          <el-input v-model="dialogControl2.form.shortName" placeholder="请输入剧场短标题" size="normal" clearable @change=""></el-input>
        </el-form-item>
        <div class="tips">
          <div>示例：</div>
          <div>
            <p>星空间89号·抓马空剧场——星空间89号</p>
            <p>沉浸式体验剧场-星空间89号——星空间89号</p>
            <p>星空间89号-抓马空剧场1号——星空间89号</p>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 认</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Theater">
import { nextTick } from 'vue'
import TheaterDetail from './TheaterDetail.vue'
import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})
const queryOptions = ref(queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
  id: undefined,
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
  dialogControl.value.form = {}
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}

function handleView(row) {
  dialogControl.value.readonly = true
  dialogControl.value.id = row.id
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.theater.findTheaterInfo, row, dialogControl.value, '查看详情')
}

function handleRecommend(row, text) {
  proxy.$api.theater.updateRecommend(row.id).then((res) => {
    proxy.$modal.msgSuccess(text)
    getList()
  })
}

function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.theater.remove, row)
}

function handleNameUpdated(data) {
  // 更新表格中的数据
  dialogControl.value.form.name = data.name
  // 刷新表格数据
  getList()
}


const dialogControl2 = ref({
  open: false,
  title: '剧场审核',
  form: {
    audit: '2',
    id: '',
    shortName: '',
    reasonsRejection: '',
  },
  readonly: false,
})
function handlePass(row) {
  // console.log(11)
  dialogControl2.value.form.name = row.name
  dialogControl2.value.form.id = row.id
  dialogControl2.value.form.shortName = ''
  dialogControl2.value.open = true
}

function submitForm() {
  proxy.$modal
    .confirm('是否确认审核通过？')
    .then((res) => {
      // console.log(33)
      proxy.$api.theater.audit(dialogControl2.value.form).then((res) => {
        proxy.$modal.msgSuccess('已通过')
        getList()
        dialogControl2.value.open = false
      })
    })
    .catch((error) => { })
}
</script>

<style lang="scss" scoped>
.auditForm {
  .title {
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 10px;
  }
  .tips {
    color: var(--el-color-info);
    font-size: 14px;
    display: flex;
    div {
      &:first-child {
        flex-shrink: 0;
      }
      p {
        margin: 0;
      }
    }
  }
}

.shortName {
  font-size: 14px;
  line-height: 18px;
  background-color: #eeeeee;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  color: #999999;
  display: inline-block;
}
</style>
