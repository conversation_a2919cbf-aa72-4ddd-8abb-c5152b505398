import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { audit_flag, recommend_flag, status_flag } = useDict('audit_flag', 'recommend_flag', 'status_flag')

export const tableConfig = {
  items: [
    { label: '剧场ID', prop: 'id', type: 'String', option: {} },
    { label: '剧场名称', prop: 'name', type: 'Slot', option: {} },
    { label: '所属商家', prop: 'merchantName', type: 'String', option: {} },
    { label: '关注人数', prop: 'focusNumber', type: 'Integer', option: {} },
    { label: '剧场地址', prop: 'address', type: 'String', option: {} },
    {
      label: '封面图',
      prop: 'coverPicture',
      type: 'Image',
      option: {
        minWidth: 160,
      },
    },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    {
      label: '二维码链接',
      prop: 'qrCode',
      type: 'QR',
      option: {
        minWidth: 160,
      },
    },
    {
      label: '是否禁用',
      prop: 'status',
      type: 'Switch',
      option: {
        tipName: 'name',
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'Audit',
      option: {
        pull: audit_flag,
        tip: 'reasonsRejection',
      },
    },
    {
      label: '是否推荐',
      prop: 'recommend',
      type: 'Dict',
      option: {
        pull: recommend_flag,
      },
    },
  ],
}

export const formConfig = {
  items: [],
  size: 'large',
}

export const queryConfig = {
  items: [
    {
      label: '所属商家',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: pull.merchantThePull,
      },
    },
    {
      label: '是否禁用',
      prop: 'status',
      type: 'select',
      option: {
        list: status_flag,
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'tags',
      option: {
        pull: audit_flag,
      },
    },
    { label: '演出时间', prop: 'showTime', type: 'daterange', option: {} },
    {
      label: '创建时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
