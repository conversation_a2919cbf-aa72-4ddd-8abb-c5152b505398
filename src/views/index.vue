<template>
  <div class="app-container">
    <el-scrollbar style="width: 100%">
      <div class="dashboard">
        <el-row :gutter="20" class="infoRow">
          <el-col :span="8">
            <el-card shadow="always" :body-style="{ padding: '20px' }">
              <div class="adminBox">
                <el-avatar
                  icon="el-icon-user-solid"
                  class="avatar"
                  :size="95"
                  shape="circle"
                  :src="user.avatar"
                  fit="fill"
                ></el-avatar>

                <div class="msg">
                  <p class="text1">欢迎你</p>
                  <p>{{ user.name }}</p>
                </div>

                <div class="loginTime">
                  <el-icon size="16"><Clock /></el-icon>
                  <div class="timeText">
                    上次登录于：{{ user.lastLoginTime }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card
              shadow="always"
              :body-style="{ padding: '10px 20px !important' }"
            >
              <template #header>
                <div class="headerTitle">
                  <div class="title">数据概览</div>
                  <div class="time">数据更新于：{{ nowTime }}</div>
                </div>
              </template>
              <div class="dataBox">
                <el-card
                  shadow="hover"
                  :body-style="{ padding: '20px 22px !important' }"
                  class="dataItem"
                  v-for="item in dataList"
                  :key="item.id"
                >
                  <div class="dataItemBox">
                    <div class="icon">
                      <svg-icon
                        :icon-class="item.icon"
                        class-name="icon"
                        style="color: #fff"
                      />
                    </div>
                    <div class="data">
                      <div class="text">
                        <div class="label">
                          <div>{{ item.label }}</div>
                        </div>
                        <div class="value">
                          <div class="num">{{ item.value }}</div>
                          <div class="addNum">{{ "+" + item.addVal }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col
            :span="12"
            v-for="chart in chartList"
            :key="chart.id"
            style="margin-bottom: 15px"
          >
            <el-card
              shadow="always"
              :body-style="{ padding: '0 20px !important' }"
            >
              <template #header>
                <div class="headerTitle">
                  <div class="title">{{ chart.label }}</div>
                  <div class="tabs">
                    <el-radio-group
                      v-model="chart.day"
                      size="small"
                      @change="handleChange(chart)"
                    >
                      <!-- <el-radio-button label="1">一天</el-radio-button> -->
                      <el-radio-button label="2">近一周</el-radio-button>
                      <el-radio-button label="3">近一月</el-radio-button>
                      <el-radio-button label="4">近一年</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
              </template>
              <div
                :ref="`chart${chart.id}`"
                style="height: 420px; width: 100%"
              ></div>
            </el-card>
          </el-col>
          <el-col :span="12"></el-col>
        </el-row>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup name="Index">
import * as echarts from "echarts";
import { computed, getCurrentInstance, nextTick } from "vue";
import useUserStore from "@/store/modules/user";
import moment from "moment";
const user = useUserStore();
// const merchant = useUserStore().merchant;
const { proxy } = getCurrentInstance();
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const { sys_user_sex } = proxy.useDict("sys_user_sex");

const dataList = ref([]);

const option1 = {
  legend: {
    data: ["剧目", "剧场"],
    left: 10,
  },
  tooltip: {
    trigger: "axis",
  },
  xAxis: {
    type: "category",
    data: [
      "4月5号",
      "4月6号",
      "4月7号",
      "4月8号",
      "4月9号",
      "4月10号",
      "4月11号",
    ],
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      name: "剧目",
      data: [150, 230, 224, 218, 135, 147, 260],
      type: "line",
      color: "#65a5f6",
    },
    {
      name: "剧场",
      data: [150, 230, 224, 218, 135, 147, 260],
      type: "line",
      color: "#5ac078",
    },
  ],
};
const option2 = {
  radar: {
    // shape: 'circle',
    indicator: [
      { name: "大众点评", max: 6500 },
      { name: "线下散客", max: 6500 },
      { name: "小红书", max: 30000 },
      { name: "抖音", max: 38000 },
      { name: "大麦", max: 52000 },
      { name: "摩天轮", max: 25000 },
      { name: "朋友推荐", max: 25000 },
    ],
    axisLine: {
      show: false,
    },
  },
  series: [
    {
      type: "radar",
      data: [
        {
          value: [4200, 3000, 20000, 35000, 50000, 18000, 3000],
        },
      ],
    },
  ],
};

const option3 = {
  tooltip: {
    trigger: "axis",
  },
  xAxis: {
    type: "category",
    data: ["白毛女"],
    // axisLabel: { interval: 0, rotate: 30 },
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      data: [120],
      type: "bar",
      color: "#65a5f6",
      label: {
        show: true,
        position: "top",
      },
      barMaxWidth: 100,
    },
  ],
  dataZoom: [
    {
      type: "slider", //slider表示有滑动块的，
      show: true,
      xAxisIndex: [0], //表示x轴折叠
      start: 1, //数据窗口范围的起始百分比,表示1%
      end: 35, //数据窗口范围的结束百分比,表示35%坐标
      bottom: "20",
    },
  ],
  axisLabel: {
    interval: 0,
    formatter: function (value, index) {
      if (value.length > 6) {
        return value.substr(0, 5) + "...";
      } else {
        return value;
      }
    },
  },
};

const option4 = {
  tooltip: {
    trigger: "axis",
  },
  xAxis: {
    type: "category",
    data: ["白毛女"],
    // axisLabel: { interval: 0, rotate: 30 },
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      data: [120],
      type: "bar",
      color: "#65a5f6",
      label: {
        show: true,
        position: "top",
      },
      barMaxWidth: 100,
    },
  ],
  dataZoom: [
    {
      type: "slider", //slider表示有滑动块的，
      show: true,
      xAxisIndex: [0], //表示x轴折叠
      start: 1, //数据窗口范围的起始百分比,表示1%
      end: 35, //数据窗口范围的结束百分比,表示35%坐标
      bottom: "20",
    },
  ],
  axisLabel: {
    interval: 0,
    formatter: function (value, index) {
      if (value.length > 6) {
        return value.substr(0, 5) + "...";
      } else {
        return value;
      }
    },
  },
};

const option5 = {
  tooltip: {
    trigger: "item",
  },
  legend: {
    orient: "vertical",
    left: "left",
  },
  series: [
    {
      name: "Access From",
      type: "pie",
      radius: "50%",
      data: [
        { value: 1048, name: "18-30岁" },
        { value: 735, name: "30-40岁" },
        { value: 580, name: "40-50岁" },
        { value: 484, name: "50岁以上" },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
      label: {
        show: true,
        formatter(param) {
          // correct the percentage
          return param.name + " (" + param.percent * 2 + "%)";
        },
      },
    },
  ],
};

const option6 = {
  tooltip: {
    trigger: "item",
  },
  legend: {
    orient: "vertical",
    left: 10,
  },
  series: [
    {
      name: "用户性别",
      type: "pie",
      radius: ["40%", "70%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: true,
      },
      label: {
        show: true,
        formatter(param) {
          // correct the percentage
          return param.name + "(" + param.value + ")";
        },
      },
      color: ["#65a5f6", "#5ac078", "#ff6666"],
      data: [
        { value: 1048, name: "男" },
        { value: 735, name: "女" },
        { value: 580, name: "未知" },
      ],
    },
  ],
};

const option7 = {
  tooltip: {
    trigger: "item",
  },
  legend: {
    orient: "vertical",
    left: "left",
  },
  series: [
    {
      name: "订单金额",
      type: "pie",
      radius: ["50%", "70%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
      label: {
        // show: true,
        // formatter(param) {
        //   // correct the percentage
        //   return param.name + "(" + param.value + ")";
        // },
        // position:'center',
        show: true,
        position: "center",
        fontWeight: "bold",
        formatter: (param) => {
          return `{a|${param.value}}\n{b|${param.name}}`;
        }, // {b}:数据名； {c}：数据值； {d}：百分比，可以自定义显示内容，
        rich: {
          a: {
            color: "#303133",
            padding: [20, 10],
            fontSize: 60,
            fontWeight: 500,
          },
          b: {
            color: "rgb(144, 156, 172)",
            padding: [0, 10],
            fontSize: 16,
          },
        },
      },
      color: ["#6899c4", "#5ac078", "#ff6666"],
      data: [{ value: 1048, name: "男" }],
    },
  ],
};

const chartList = ref([
  {
    id: 1,
    label: "关注动态",
    options: option1,
    day: 2,
  },
  // {
  //   id:2,
  //   label:'用户来源',
  //   options:option2,
  //   day:1
  // },
  {
    id: 3,
    label: "剧目占比",
    options: option3,
    day: 2,
  },
  {
    id: 4,
    label: "剧场占比",
    options: option4,
    day: 2,
  },
  // {
  //   id:5,
  //   label:'用户年龄',
  //   options:option5,
  //   day:1
  // },
  {
    id: 6,
    label: "用户性别",
    options: option6,
    day: 2,
  },
  {
    id: 7,
    label: "订单金额",
    options: option7,
    day: 2,
  },
]);

const nowTime = ref("");

/* 数据概览 */
function getData() {
  /* 数据概览 */
  dataList.value = [
    {
      id: 1,
      label: "商家",
      value: "",
      addVal: "10",
      icon: "data-store",
    },
    {
      id: 2,
      label: "剧场",
      value: "999",
      addVal: "10",
      icon: "data-theater",
    },
    {
      id: 3,
      label: "剧目",
      value: "999",
      addVal: "10",
      icon: "data-play",
    },
    {
      id: 4,
      label: "电子票领取人次",
      value: "999",
      addVal: "10",
      icon: "data-bill",
    },
    {
      id: 5,
      label: "数字头像领取人次",
      value: "999",
      addVal: "10",
      icon: "data-avatar",
    },
    {
      id: 6,
      label: "纪念徽章领取人次",
      value: "999",
      addVal: "10",
      icon: "data-medal",
    },
  ];

  const params = {
    beginTime: moment().format("YYYY-MM-DD"),
    endTime: moment().format("YYYY-MM-DD"),
  };
  nowTime.value = moment().format("YYYY-MM-DD HH:mm:ss");
  /* 商家 */
  proxy.$api.merchant.findMerchantAddCount({}).then((res) => {
    nextTick(() => {
      dataList.value[0].value = res.data;
      proxy.$api.merchant.findMerchantAddCount(params).then((res) => {
        dataList.value[0].addVal = res.data;
      });
    });
  });

  /* 剧场 */
  proxy.$api.theater.findTheaterAddCount({}).then((res) => {
    nextTick(() => {
      dataList.value[1].value = res.data;
      proxy.$api.theater.findTheaterAddCount(params).then((res) => {
        dataList.value[1].addVal = res.data;
      });
    });
  });

  /* 剧目 */
  proxy.$api.repertoire.findRepertoireAddCount({}).then((res) => {
    nextTick(() => {
      dataList.value[2].value = res.data;
      proxy.$api.repertoire.findRepertoireAddCount(params).then((res) => {
        dataList.value[2].addVal = res.data;
      });
    });
  });

  /* 电子票、数字头像、纪念徽章 */
  proxy.$api.userReceivingRecords
    .findUserReceivingRecordsAddCount({
      badgeType: 1, //电子票
    })
    .then((res) => {
      nextTick(() => {
        dataList.value[3].value = res.data;
        proxy.$api.userReceivingRecords
          .findUserReceivingRecordsAddCount({
            time: params,
            badgeType: 1,
          })
          .then((res) => {
            dataList.value[3].addVal = res.data;
          });
      });
    });
  proxy.$api.userReceivingRecords
    .findUserReceivingRecordsAddCount({
      badgeType: 2, //数字头像
    })
    .then((res) => {
      nextTick(() => {
        dataList.value[4].value = res.data;
        proxy.$api.userReceivingRecords
          .findUserReceivingRecordsAddCount({
            time: params,
            badgeType: 2,
          })
          .then((res) => {
            dataList.value[4].addVal = res.data;
          });
      });
    });

  proxy.$api.userReceivingRecords
    .findUserReceivingRecordsAddCount({
      badgeType: 3, //纪念徽章
    })
    .then((res) => {
      nextTick(() => {
        dataList.value[5].value = res.data;
        proxy.$api.userReceivingRecords
          .findUserReceivingRecordsAddCount({
            time: params,
            badgeType: 3,
          })
          .then((res) => {
            dataList.value[5].addVal = res.data;
          });
      });
    });
}

/* 仪表图 */
function getChart() {
  chartList.value.forEach((chart) => {
    // console.log(proxy.$refs)
    var initChart = echarts.init(proxy.$refs[`chart${chart.id}`][0]);
    initChart.setOption(chart.options);
  });
}

function handleChange(chart) {
  var time = {
    beginTime: undefined,
    endTime: moment().format("YYYY-MM-DD HH:mm:ss"),
  };
  if (chart.day == 1) {
    // console.log(22)
    // const
  } else if (chart.day == 2) {
    time.beginTime = moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss");
  } else if (chart.day == 3) {
    time.beginTime = moment()
      .subtract(1, "months")
      .format("YYYY-MM-DD HH:mm:ss");
  } else if (chart.day == 4) {
    time.beginTime = moment()
      .subtract(1, "years")
      .format("YYYY-MM-DD HH:mm:ss");
  }

  /* 关注人数 */
  if (chart.id == 1) {
    proxy.$api.userTreasure
      .findUserTreasureAddCount({
        ...time,
      })
      .then((res) => {
        // console.log(res)`
        const labels = res.data.map((i) => i.time);
        const values1 = res.data.map((i) => i.repertoireCount);
        const values2 = res.data.map((i) => i.theaterCount);
        option1.xAxis.data = labels;
        option1.series[0].data = values1;
        option1.series[1].data = values2;
        // echarts.init(proxy.$refs[`chart1`][0]).setOption(option1);
        const mychart = echarts.init(proxy.$refs[`chart1`][0]);
        mychart.setOption(option1);
        window.addEventListener("resize", () => {
          mychart.resize();
        });
      });
  }

  if (chart.id == 6) {
    // console.log(findArrValue(sys_user_sex.value, 'value', 0))
    proxy.$api.user
      .findUserSexCount({
        ...time,
      })
      .then((res) => {
        const data = [
          { value: 0, name: "男", type: 0 },
          { value: 0, name: "女", type: 1 },
          { value: 0, name: "未知", type: 2 },
        ];
        res.data.forEach((item1) => {
          data.forEach((item2) => {
            if (item1.type == item2.type) {
              item2.value = item1.count || 0;
            }
          });
        });
        option6.series[0].data = data.map((i) => ({
          value: i.value,
          name: i.name,
        }));
        const mychart = echarts.init(proxy.$refs[`chart6`][0]);
        mychart.setOption(option6);
        window.addEventListener("resize", () => {
          mychart.resize();
        });
        // console.log(2323232)
      });
  }

  if (chart.id == 3) {
    proxy.$api.userTreasure
      .findUserTreasureRepertoireCount({
        ...time,
      })
      .then((res) => {
        option3.xAxis.data = res.data.map((i) => i.type);
        option3.series[0].data = res.data.map((i) => i.count);
        if (res.data.length > 4) {
          option3.xAxis.axisLabel = { interval: 0, rotate: 30 };
        }
        // echarts.init(proxy.$refs[`chart3`][0]).setOption(option3);
        const mychart = echarts.init(proxy.$refs[`chart3`][0]);
        mychart.setOption(option3);
        window.addEventListener("resize", () => {
          mychart.resize();
        });
      });
  }
  if (chart.id == 4) {
    proxy.$api.userTreasure
      .findUserTreasureTheaterCount({
        ...time,
      })
      .then((res) => {
        option4.xAxis.data = res.data.map((i) => i.type);
        option4.series[0].data = res.data.map((i) => i.count);
        if (res.data.length > 4) {
          option4.xAxis.axisLabel = { interval: 0, rotate: 30 };
        }
        // echarts.init(proxy.$refs[`chart4`][0]).setOption(option4);
        const mychart = echarts.init(proxy.$refs[`chart4`][0]);
        mychart.setOption(option4);
        window.addEventListener("resize", () => {
          mychart.resize();
        });
      });
  }
  if (chart.id == 7) {
    proxy.$api.userOrder
      .findOrderSumPrice({
        ...time,
      })
      .then((res) => {
        const data = [{ value: 0, name: "成交订单总额", type: 0 }];
        // console.log('sex,',sys_user_sex.value)
        data[0].value = res.data || 0;

        // console.log('sexData',data, res.data)
        option7.series[0].data = data.map((i) => ({
          value: i.value,
          name: i.name,
        }));
        const mychart = echarts.init(proxy.$refs[`chart7`][0]);
        // console.log(2323232)
        mychart.setOption(option7);
        window.addEventListener("resize", () => {
          mychart.resize();
        });
      });
  }
}

function findArrValue(arr, param, val) {
  // console.log(arr, val)
  if (Array.isArray(arr)) {
    const afterFilter = arr.filter((i) => i[param] == val);
    if (afterFilter.length) {
      return afterFilter[0];
    } else {
      return {};
    }
  }
}

onMounted(() => {
  // getChart();
  chartList.value.forEach((chart) => {
    handleChange(chart);
  });

  getData();
});
</script>

<style scoped lang="scss">
.dashboard {
  width: 100%;
  .el-row {
    width: 100%;
    &.infoRow {
      height: 342px;
      margin-bottom: 24px;
      .el-col {
        height: 100%;
        .el-card {
          height: 100%;
        }
      }
    }
  }
  :deep(.el-card__header) {
    padding: 25px 32px 15px !important;
    border: none;
  }
  .adminBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 16px;
    .avatar {
      position: absolute;
      top: 53px;
    }
    .msg {
      max-width: 238px;
      width: 100%;
      // height: 128px;
      padding: 32px 0;
      text-align: center;
      background-color: #f5f5f5;
      border-radius: 5px;
      margin-top: 115px;
      margin-bottom: 28px;
      p {
        margin: 0;
        font-size: 16px;
        text-align: center;
        font-family: SourceHanSansSC-regular;
        line-height: 32px;
        font-weight: 600;
      }
    }
    .loginTime {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(144, 156, 172, 1);
      .timeText {
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        margin-left: 8px;
      }
    }
  }
  .dataBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-around;
    // margin-right: -50px;
    .dataItem {
      width: 100%;
      max-width: 30%;
      height: 100px;
      margin-bottom: 18px;
      margin-right: 5%;
      &:nth-child(3n) {
        margin-right: 0;
      }
      // display: flex;
      // .
      .dataItemBox {
        display: flex;
        align-items: center;
        width: 100%;
        .icon {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          // color: #ffffff;
          .icon {
            font-size: 70px;
            // color: #ffffff !important;
          }
        }
        .data {
          flex-grow: 1;
          margin-left: 8px;
          height: 72px;
          .text {
            width: fit-content;
            margin: 0 auto;
            text-align: center;
            .label {
              color: rgba(144, 156, 172, 1);
              font-size: 14px;
              line-height: 20px;
              font-family: SourceHanSansSC-regular;
              min-height: 32px;
              // div{
              //   line-height: 10px;
              // }
            }
            .value {
              display: flex;
              justify-content: center;
              line-height: 40px;
              .num {
                font-size: 18px;
              }
              .addNum {
                color: #28b269;
                font-size: 14px;
                font-family: SourceHanSansSC-regular;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }

  .headerTitle {
    display: flex;
    .title {
      flex-grow: 1;
      font-weight: 600;
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
    .time {
      color: rgba(108, 108, 108, 0.7);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}
</style>
