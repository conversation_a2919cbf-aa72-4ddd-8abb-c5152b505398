<template>
  <div class="app-container">
    <HandleRow v-model:query="queryParams" @search="getList" placeholder="请输入剧目名称" />

    <el-table v-loading="loading" :data="tableData" flexible @sort-change="hadnleSortChange" ref="tableRef">
      <el-table-column label="序号" align="center" prop="index" width="55" fixed />
      <el-table-column label="剧目名称" align="center" prop="name" width="400" fixed />
      <el-table-column :label="i" align="center" sortable="custom" :prop="`leaderboardValue${index}`" min-width="200" v-for="(i, index) in headerName1">
        <template #default="scope">{{ scope.row.leaderboardList[index].count }}</template>
      </el-table-column>
      <el-table-column :label="i" align="center" sortable="custom" :prop="`ticketGroupValue${index}`" min-width="200" v-for="(i, index) in headerName2">
        <template #default="scope">{{ scope.row.ticketGroupList[index].count }}</template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="User">
import bus from '@/utils/bus'

const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  id: undefined,
  orderByColumn: undefined,
  isAsc: undefined,
})

const loading = ref(false)
const headerName1 = ref([])
const headerName2 = ref([])
const tableData = ref([])
const total = ref(0)

onMounted(() => {
  bus.on('searchReset', () => {
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      keyword: undefined,
      orderByColumn: undefined,
      isAsc: undefined,
    }
    proxy.$refs.tableRef.clearSort()

    getList()
  })

  getList()
})

const getList = () => {
  loading.value = true

  proxy.$api.leaderboard
    .repertoireLeaderboard(queryParams.value)
    .then((response) => {
      let temp1 = [],
        temp2 = []

      response.data.rows[0].leaderboardList.map((j, index) => {
        temp1.push(j.name)
      })

      response.data.rows[0].ticketGroupList.map((j, index) => {
        temp2.push(j.name)
      })

      response.data.rows.forEach((i) => (i.index = (queryParams.value.pageNum - 1) * queryParams.value.pageSize + response.data.rows.indexOf(i) + 1))

      headerName1.value = temp1
      headerName2.value = temp2
      tableData.value = response.data.rows
      total.value = response.data.total
      loading.value = false
    })
    .catch((err) => {
      loading.value = false
    })
}

const hadnleSortChange = ({ column, prop, order }) => {
  let reg1 = new RegExp('leaderboardValue')
  let reg2 = new RegExp('ticketGroupValue')

  let type = 1
  let oindex = ''
  let id = ''
  let orderByColumn = ''

  if (reg1.test(prop)) {
    type = 1
    oindex = prop.replace('leaderboardValue', '')
    id = tableData.value[0].leaderboardList[oindex].id
    orderByColumn = 'leaderboardCount'
  }

  if (reg2.test(prop)) {
    type = 2
    oindex = prop.replace('ticketGroupValue', '')
    id = tableData.value[0].ticketGroupList[oindex].id
    orderByColumn = 'ticketGroupCount'
  }

  queryParams.value.id = order ? id : undefined
  queryParams.value.orderByColumn = order ? orderByColumn : undefined
  queryParams.value.isAsc = order ? (order === 'ascending' ? 'asc' : 'desc') : undefined
  getList()
}
</script>

<style lang="scss">
.el-table .disabled-row {
  color: #999999;
  .intro {
    .info {
      .name {
        color: #999999;
      }
    }
  }
}
</style>
