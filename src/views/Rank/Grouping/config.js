import { useDict } from '@/utils/dict'

const { status_flag } = useDict('status_flag')

export const tableConfig1 = {
  items: [
    { label: '分组名', prop: 'name', type: 'String', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    { label: '排序', prop: 'sort', type: 'String', option: {} },
    { label: '启用', prop: 'status', type: 'Switch', option: {} },
  ],
}

export const tableConfig2 = {
  items: [
    { label: '榜单名', prop: 'name', type: 'String', option: {} },
    { label: '创建时间', prop: 'createTime', type: 'Time', option: {} },
    { label: '排序', prop: 'sort', type: 'String', option: {} },
    { label: '启用', prop: 'status', type: 'Switch', option: {} },
  ],
}

export const formConfig1 = {
  items: [
    { label: '分组名', prop: 'name', type: 'String', option: {}, rules: [{ required: true, message: '请输入分组名', trigger: '' }], size: 'default' },
    { label: '排序', prop: 'sort', type: 'String', option: {}, size: 'default' },
    {
      label: '启用',
      prop: 'status',
      type: 'Radio',
      option: {
        pull: status_flag,
      },
      size: 'default',
    },
  ],
  size: 'small',
}

export const formConfig2 = {
  items: [
    { label: '榜单名', prop: 'name', type: 'String', option: {}, rules: [{ required: true, message: '请输入分组名', trigger: '' }], size: 'default' },
    { label: '排序', prop: 'sort', type: 'String', option: {}, size: 'default' },
    {
      label: '启用',
      prop: 'status',
      type: 'Radio',
      option: {
        pull: status_flag,
      },
      size: 'default',
    },
  ],
  size: 'small',
}
