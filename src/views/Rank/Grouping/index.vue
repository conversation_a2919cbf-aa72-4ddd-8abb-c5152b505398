<template>
  <div class="app-container">
    <HandleRow v-model:query="queryParams" @search="getList" :placeholder="queryParams.tableType === 0 ? '请输入分组名' : '请输入榜单名'" :needTab="true">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </template>
    </HandleRow>

    <!-- 分组 -->
    <CommonTable
      :listReq="$api.ticketGroup.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols1"
      :statusReq="$api.ticketGroup.updateStatus"
      v-model:multiple="multiple"
      ref="tableRef"
      v-if="queryParams.tableType === 0">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </CommonTable>

    <!-- 榜单 -->
    <CommonTable
      :listReq="$api.leaderboard.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols2"
      :statusReq="$api.leaderboard.updateStatus"
      v-model:multiple="multiple"
      ref="tableRef"
      v-else-if="queryParams.tableType === 1">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig1.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef">
      <CommonForm
        v-model:form="dialogControl.form"
        :formOptions="formOptions1"
        :readonly="dialogControl.readonly"
        ref="formRef"
        v-if="queryParams.tableType === 0" />

      <CommonForm
        v-model:form="dialogControl.form"
        :formOptions="formOptions2"
        :readonly="dialogControl.readonly"
        ref="formRef"
        v-else-if="queryParams.tableType === 1" />

      <template #footer v-if="!dialogControl.readonly">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Answer">
import { formConfig1, formConfig2, tableConfig1, tableConfig2 } from './config'
const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryParams = ref({
  tableType: 0,
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
})

const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols1 = ref(tableConfig1.items)
const tableCols2 = ref(tableConfig2.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
})
const formOptions1 = ref(formConfig1.items)
const formOptions2 = ref(formConfig2.items)

// 重置
function reset() {
  dialogControl.value.form = {}
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
/* 添加 */
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, queryParams.value.tableType === 0 ? '添加分组' : '添加榜单')
}
/* 修改 */
function handleUpdate(row) {
  if (queryParams.value.tableType === 0) proxy.$refs.tableRef.handleGetDetail(proxy.$api.ticketGroup.detail, row, dialogControl.value, '修改分组')
  else proxy.$refs.tableRef.handleGetDetail(proxy.$api.leaderboard.detail, row, dialogControl.value, '修改榜单')
}
/* 删除 */
function handleDelete(row) {
  console.log(queryParams.value.tableType)
  if (queryParams.value.tableType === 0) proxy.$refs.tableRef.handleDelete(proxy.$api.ticketGroup.remove, row)
  else proxy.$refs.tableRef.handleDelete(proxy.$api.leaderboard.remove, row)
}
/* 提交 */
function submitForm() {
  proxy.$refs.formRef.formRef.validate((valid) => {
    if (valid) {
      dialogControl.value.form.type = 0
      if (queryParams.value.tableType === 0) proxy.$refs.tableRef.handleSubmit(proxy.$api.ticketGroup.add, proxy.$api.ticketGroup.update, dialogControl.value)
      else proxy.$refs.tableRef.handleSubmit(proxy.$api.leaderboard.add, proxy.$api.leaderboard.update, dialogControl.value)
    }
  })
}
</script>
