import useUserStore from '@/store/modules/user'
import { useDict } from '@/utils/dict'
const pull = useUserStore().pull
const { reply_status } = useDict('reply_status')

export const tableConfig = {
  items: [
    {
      label: '用户头像',
      prop: 'userAvatar',
      type: 'Avatar',
      option: {
        minWidth: 120,
      },
    },
    { label: '用户昵称', prop: 'userName', type: 'String', option: {} },
    {
      label: '问答内容',
      prop: 'content',
      type: 'String',
      option: {
        minWidth: 300,
        showTip: true,
      },
    },
    { label: '关联剧目', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    { label: '提问时间', prop: 'createTime', type: 'Time', option: {} },
    { label: '回复状态', prop: 'replyStatus', type: 'Slot', option: {} },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '剧场名称',
      prop: 'theaterId',
      type: 'multipleSelect',
      option: {
        pull: pull.theater,
      },
    },
    {
      label: '剧目名称',
      prop: 'repertoireId',
      type: 'multipleSelect',
      option: {
        pull: pull.repertoire,
      },
    },
    {
      label: '提问时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
