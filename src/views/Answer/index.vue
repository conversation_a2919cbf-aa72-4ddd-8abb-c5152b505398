<template>
    <div class="app-container">
        <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" :needPart="true"/>
        <HandleRow v-model:query="queryParams" @search="getList" placeholder="用户名称 / 问答内容" :needPart="true">
            <template #btns>
                <el-col :span="1.5">
                    <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
                </el-col>
            </template>
        </HandleRow>
        <CommonTable :listReq="$api.issue.listByPage" :queryParams="queryParams" :tableCols="tableCols" :needPart="true"
            v-model:multiple="multiple" ref="tableRef">
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
                <template #default="scope">
                    <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>
                    <el-button link type="danger" @click="handleReport(scope.row)" :disabled="scope.row.status == 0">
                        {{ scope.row.status == 0?'已举报':'举报' }}
                    </el-button>
                    
                </template>
            </el-table-column>
            <template #replyStatus="{ data }">
                <span v-if="data.scope.row.replyCount > 0">{{
                `有${data.scope.row.replyCount}个人回复`
                }}</span>
                <span v-else>{{ `暂无回复` }}</span>
            </template>
        </CommonTable>

        <!-- 添加或修改公告对话框 -->
        <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
        @closed="cancel"
            ref="dialogRef">
            <ReplyForm type="answer" :form = "dialogControl.form"/>
        </el-dialog>
    </div>
</template>
 
<script setup name="Answer">
import { nextTick } from 'vue';
import ReplyForm from '../Comment/detail/replyForm.vue';

import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance();


/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    merchantCategory:'1'
})
const queryOptions = ref(queryConfig.items)
const getList = () => { proxy.$refs.tableRef.getList() }
const multiple = ref(true)
const tableCols = ref(tableConfig.items)


/* 操作和表单配置 */
const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
    dialogControl.value.form = {
    };
    // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
    dialogControl.value.open = false;
    dialogControl.value.readonly = false
    reset();
}

function handleView(row) {
    dialogControl.value.readonly = true
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.issue.detail, row, dialogControl.value, '查看详情')
}

function handleReport(row){
    proxy.$modal.confirm('您确认举报这条问答吗？').then(res => {
        proxy.$api.issue.report(row.id).then(res => {
            proxy.$modal.msgSuccess('举报成功')
            getList()
        })
    }).catch(err=> {

    })
    
}

</script>

 