<template>
    <div class="app-container">
        <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
        <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧目名称/剧场名称"  v-if="!props.disableSearch">
            <template #btns>
                <el-col :span="1.5">
                    <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
                </el-col>
            </template>
        </HandleRow>
        <CommonTable :listReq="$api.repertoireTicket.listByPage" :queryParams="queryParams" :tableCols="tableCols"
            v-model:multiple="multiple" ref="tableRef">
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
                <template #default="scope">
                    <!-- <BtnForCheck :req="$api.repertoireTicket.audit" :disabled="scope.row.audit" :rowId="scope.row.id" @change="getList" hasPassEvent @pass="billPass(scope.row)"/> -->
                    <BtnForCheck :req="$api.repertoireTicket.audit" :disabled="scope.row.audit" :rowId="scope.row.id" @change="getList"/>
                    <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>

        </CommonTable>

        <!-- 添加或修改公告对话框 -->
        <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
        @closed="cancel"
            ref="dialogRef">
            <CommonForm v-model:form = "dialogControl.form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef"/>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="cancel">取 消</el-button> -->
                </div>
            </template>
        </el-dialog>

        <!-- <img :src="imgview"  alt=""> -->
    </div>  
</template>
 
<script setup name="Bill">
import { nextTick } from 'vue';
import { tableConfig, formConfig, queryConfig } from './config'


const { proxy } = getCurrentInstance();

const props = defineProps({
    repertoireId:{
        type:String,
        default:undefined
    },
    disableSearch:{
        type:Boolean,
        default:false
    }
})
/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    repertoireId: props.repertoireId || undefined
})
const queryOptions = ref(queryConfig.items)
const getList = () => { proxy.$refs.tableRef.getList() }
const multiple = ref(true)
const tableCols = ref(tableConfig.items)


/* 操作和表单配置 */
const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
    dialogControl.value.form = {
    };
    // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
    dialogControl.value.open = false;
    dialogControl.value.readonly = false
    reset();
}
function handleAdd() {
    reset()
    proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加商家')
}
function handleView(row) {
    dialogControl.value.readonly = true
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.repertoireTicket.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
    proxy.$refs.tableRef.handleGetDetail(proxy.$api.repertoireTicket.detail, row, dialogControl.value, '修改电子票')
}
function handleDelete(row) {
    proxy.$refs.tableRef.handleDelete(proxy.$api.repertoireTicket.remove, row)
}
function submitForm() {
    // console.log(proxy.$refs["formRef"].formRef.validate)
    proxy.$refs["formRef"].formRef.validate(valid => {
        if (valid) {
            proxy.$refs.tableRef.handleSubmit(proxy.$api.repertoireTicket.add, proxy.$api.repertoireTicket.update, dialogControl.value)
        }
    });
}


onMounted(() => {
    
})

/* 拼合电子票正反面 */
const imgview = ref('')
const baseUrl = import.meta.env.VITE_APP_BASE_API;

function billPass(row){
    imgview.value = ''
    combineBill(row.coverFront, row.coverReverse, row.id)
}
function combineBill(cover1,cover2,id){
        const imgArr = [baseUrl+cover1, baseUrl+cover2]
        let dom = document.createElement('canvas')
        combineImg(imgArr, dom, handlePic)

        function handlePic(pic64){
            imgview.value = pic64
            console.log('打印图像', imgview.value)


            /* 调取审核接口 */
            proxy.$api.repertoireTicket.audit({
                id: id,
                audit: "2",
                repertoireTicketUrl:imgview.value
            }).then(res => {
                proxy.$modal.msgSuccess('已通过')
                getList()
            })
                
        }
}
function combineImg(imgArray, dom, callback){
    const canvas = dom
    // console.log(canvas)
    const ctx = canvas.getContext('2d')
    
    var count = 0
    var positionX = 0
    var pic64 = ''
    var quality = 0.92
    var targSize = 500 * 500
    function draw(){
        if(count === imgArray.length){
            pic64 = canvas.toDataURL("image/webp",quality)

            while (pic64.length > targSize) {
                quality -= 0.05;
                console.log(pic64.length + "循环压缩" + quality)
                pic64 = canvas.toDataURL("image/webp", quality);
            }


            const slicePic64 = pic64.split(',')[1]
            // pic64.splice(0,pic64.indexOf(','))
            // console.log('pic64', pic64)
            // console.log()
            callback(slicePic64)
            
            // console.log('绘制完成', pic64)
            
            return
        }
        var imgDom = new Image()
        imgDom.src = imgArray[count]
        imgDom.setAttribute("crossOrigin",'Anonymous')
        imgDom.onload = function(){
            console.log(this)
            // console.log(count,imgDom)
            if(count == 0){
                canvas.width = this.width * 2;
                canvas.height = this.height;
                positionX = this.width
                ctx.drawImage(imgDom, 0,0,this.width,this.height)
            }
            else{
                ctx.drawImage(imgDom, positionX,0,this.width,this.height)
            }
            
            // canvasToBase64()
            count++
            draw()
        }
    }
    // console.log('开始绘制')  
    draw()
}

</script>

 