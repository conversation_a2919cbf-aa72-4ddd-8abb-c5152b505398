<template>
    <div class="app-container">
        <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />
        <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧场名称/剧目名称" v-if="!props.disableSearch">
            <template #btns>
                <el-col :span="1.5">
                    <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
                </el-col>
            </template>
        </HandleRow>
        <CommonTable :listReq="$api.digitalAvatar.listByPage" :queryParams="queryParams" :tableCols="tableCols"
            v-model:multiple="multiple" ref="tableRef">
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right">
                <template #default="scope">
                    <!-- <BtnForCheck :req="$api.digitalAvatar.audit" :disabled="scope.row.audit" :rowId="scope.row.id" @change="getList" hasPassEvent @pass="avatarPass(scope.row)" /> -->
                    <BtnForCheck :req="$api.digitalAvatar.audit" :disabled="scope.row.audit" :rowId="scope.row.id" @change="getList"/>
                    <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    <!-- <el-button link type="danger" @click="combineAvatar(scope.row.digitalAvatarImageList)">pinhe</el-button> -->
                    
                </template>
            </el-table-column>

        </CommonTable>

        <!-- 添加或修改公告对话框 -->
        <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false"
        @closed="cancel"
            ref="dialogRef">
            <CommonForm v-model:form = "form" :formOptions = "formOptions" :readonly="dialogControl.readonly" ref="formRef"/>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="cancel">取 消</el-button> -->
                </div>
            </template>
        </el-dialog>

        <!-- <canvas ref="canvas" id="canvas"></canvas> -->


        <!-- <img :src="item" v-for="item in imgList" alt=""> -->
    </div>
</template>
 
<script setup name="Avatar">
import { h, nextTick } from 'vue';
import { tableConfig, formConfig, queryConfig } from './config'
const { proxy } = getCurrentInstance();

const props = defineProps({
    repertoireId:{
        type:String,
        default:undefined
    },
    disableSearch:{
        type:Boolean,
        default:false
    }
})

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    keyword: undefined,
    repertoireId: props.repertoireId || undefined
})
const queryOptions = ref(queryConfig.items)
const getList = () => { proxy.$refs.tableRef.getList() }
const multiple = ref(true)
const tableCols = ref(tableConfig.items)


/* 操作和表单配置 */
const dialogControl = ref({
    open: false,
    title: '',
    form: {},
    readonly: false
})
const formOptions = ref(formConfig.items)
// 重置
function reset() {
    dialogControl.value.form = {

    };
    // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
    dialogControl.value.open = false;
    dialogControl.value.readonly = false
    reset();
}


function handleDelete(row) {
    proxy.$refs.tableRef.handleDelete(proxy.$api.digitalAvatar.remove, row)
}

/* 拼合数字头像 */
const imgList = ref([])
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const avatarWidth = 800 //头像大小

function avatarPass(row){
    proxy.$modal.confirm("数字头像的审核通过，需要对配件图片进行组合重绘，配件图片越多，重绘时间越长，请耐心等待！").then(res => {
        imgList.value = []
        // console.log('创建',row)
        combineAvatar(row.digitalAvatarImageList, row.id, row.issuedQuantity, 0)
    }).catch(err => {})
}
function combineAvatar(avatarList,id, count, init=0){
    var groupArr = Object.values(proxy.groupBy(i=>i.group, avatarList))
    // console.log(proxy.arrayPermutations(groupArr), 'dayn')
    const hasCombined = proxy.arrayPermutations(groupArr).map(arr => {
        return arr.map(i => baseUrl + i.image)
    })
    // combineImg(hasCombined[0])
    hasCombined.forEach((array, index) => {
        // h('canvas', {ref:`canvasRef${index}`})
        let dom = document.createElement('canvas')
        if(index + 1 > init && index < init+count ){
            combineImg(array,dom,handlePic)
        }

        function handlePic(pic64){
            imgList.value.push(pic64)
            console.log('打印图像', imgList.value)
            if(imgList.value.length === hasCombined.length || imgList.value.length === count){
                console.log('可以发送', imgList.value)
                /* 调取审核接口 */
                proxy.$api.digitalAvatar.audit({
                    id: id,
                    audit: "2",
                    digitalAvatarUrl:imgList.value
                }).then(res => {
                    proxy.$modal.msgSuccess('已通过')
                    getList()
                })
                
            }
        }
        // result.push(img64)
    })
    // return result
    // console.log('打印图像', imgList.value)
    // imgList.value = img64
}
function combineImg(imgArray, dom, callback){
    const canvas = dom
    // console.log(canvas)
    const ctx = canvas.getContext('2d')
    canvas.width = avatarWidth;
    canvas.height = avatarWidth;
    ctx.clearRect(0,0,avatarWidth,avatarWidth)
    
    var count = 0
    var pic64 = ''
    var quality = 0.92
    var targSize = 500*500
    function draw(){
        if(count === imgArray.length){
            pic64 = canvas.toDataURL("image/webp",quality)


            while (pic64.length > targSize) {
                quality -= 0.05;
                console.log(pic64.length + "循环压缩" + quality)
                pic64 = canvas.toDataURL("image/webp", quality);
            }


            const slicePic64 = pic64.split(',')[1]

            callback(slicePic64)
            
            // console.log('绘制完成', pic64)
            
            return
        }
        var imgDom = new Image()
        imgDom.src = imgArray[count]
        imgDom.setAttribute("crossOrigin",'Anonymous')
        imgDom.onload = function(){
            // console.log(count,imgDom)
            ctx.drawImage(imgDom, 0,0,avatarWidth,avatarWidth)
            // canvasToBase64()
            count++
            draw()
        }
    }
    // console.log('开始绘制')  
    draw()
}


</script>

 