import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
const pull = useUserStore().pull
const { audit_flag } = useDict('audit_flag')

export const tableConfig = {
  items: [
    // { label: '编码', prop: 'code', type: 'string', option: {} },
    { label: '剧目名称', prop: 'repertoireName', type: 'String', option: {} },
    { label: '关联剧场', prop: 'theaterName', type: 'String', option: {} },
    { label: '发行方', prop: 'issuerName', type: 'String', option: {} },
    {
      label: '数字头像配件',
      prop: 'digitalAvatarImageList',
      type: 'Image',
      option: {
        changeFnc: (data) => {
          if (Array.isArray(data) && data.length > 0) {
            const arr = data.map((i) => {
              return i.image
            })
            console.log(arr.join(','))
            return arr.join(',')
          } else {
            return ''
          }
        },
        picNumMax: 6,
        minWidth: 330,
        width: 300,
      },
    },

    {
      label: '发行时间',
      prop: 'startTime',
      type: 'issueTime',
      option: {
        minWidth: 300,
      },
    },
    { label: '发行数量', prop: 'issuedQuantity', type: 'Integer', option: {} },
    // { label: '领取条件', prop: 'issuedQuantity', type: 'Integer', option: {} },
    {
      label: '领取数量',
      prop: 'getCount',
      type: 'receivedNumber',
      option: {
        type: 2,
      },
    },

    {
      label: '审核状态',
      prop: 'audit',
      type: 'Audit',
      option: {
        pull: audit_flag,
        tip: 'reasonsRejection',
      },
    },

    {
      label: '上架状态',
      prop: 'hasSold',
      type: 'hasSold',
      option: {},
    },
  ],
}
export const formConfig = {
  items: [],
  size: 'small',
}

export const queryConfig = {
  items: [
    {
      label: '商家名称',
      prop: 'merchantId',
      type: 'multipleSelect',
      option: {
        pull: pull.merchantRepPull,
      },
    },
    {
      label: '审核状态',
      prop: 'audit',
      type: 'tags',
      option: {
        pull: audit_flag,
      },
    },
    {
      label: '发行时间',
      prop: 'time',
      type: 'daterange',
      option: {},
    },
  ],
}
