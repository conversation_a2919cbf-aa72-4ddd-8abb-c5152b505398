<template>
  <div class="app-container">
    <SearchForm
      v-model:open="queryOpen"
      :options="queryOptions"
      v-model:query="queryParams"
      @search="getList"
    />
    <HandleRow
      v-model:query="queryParams"
      @search="getList"
      placeholder="徽章名称"
      v-if="!props.disableSearch"
    >
      <template #btns>
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info"
            >高级筛选</el-button
          >
        </el-col>
      </template>
    </HandleRow>
    <CommonTable
      :listReq="$api.souvenirBadge.listByPage"
      :queryParams="queryParams"
      :tableCols="tableCols"
      v-model:multiple="multiple"
      ref="tableRef"
    >
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="120"
        fixed="right"
      >
        <template #default="scope">
          <BtnForCheck
            :req="$api.souvenirBadge.audit"
            :disabled="scope.row.audit"
            :rowId="scope.row.id"
            @change="getList"
          />
          <el-button link type="danger" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>

      <template #receive="{ data }">
        <div class="">
          <div v-if="data.scope.row.souvenirBadgeRequire?.rankMedalLevel">
            {{
              "勋章等级大于等于" +
              data.scope.row.souvenirBadgeRequire?.rankMedalLevel
            }}
          </div>
          <div v-if="data.scope.row.souvenirBadgeRequire?.lookNumber">
            {{
              "电子票扫描" +
              data.scope.row.souvenirBadgeRequire?.lookNumber +
              "次"
            }}
          </div>
          <div v-if="data.scope.row.souvenirBadgeRequire?.repertoireStartTime">
            {{
              "观看演出" +
              moment(data.scope.row.souvenirBadgeRequire?.repertoireStartTime).format('YYYY-MM-DD HH:mm') +
              "-" +
              moment(data.scope.row.souvenirBadgeRequire?.repertoireEndTime).format('YYYY-MM-DD HH:mm') + '场次' 
            }}
          </div>
          <div v-if="data.scope.row.souvenirBadgeRequire?.startTime">
            {{
              "时间段" +
              data.scope.row.souvenirBadgeRequire?.startTime +
              "-" +
              data.scope.row.souvenirBadgeRequire?.endTime+' 内观看演出' +
              (data.scope.row.souvenirBadgeRequire?.timeLookNumber?` ${data.scope.row.souvenirBadgeRequire?.timeLookNumber}次`:'')
            }}
          </div>
          <!-- <div>{{ '指定名单发放' +  data.scope.row.souvenirBadgeRequire.souvenirBadgeId }}</div> -->
        </div>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize(formConfig.size)"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >
      <CommonForm
        v-model:form="form"
        :formOptions="formOptions"
        :readonly="dialogControl.readonly"
        ref="formRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Emblem">
import { nextTick } from "vue";
import { tableConfig, formConfig, queryConfig } from "./config";
import moment from 'moment'
const { proxy } = getCurrentInstance();

const props = defineProps({
  theaterId: {
    type: String,
    default: undefined,
  },
  disableSearch: {
    type: Boolean,
    default: false,
  },
});

/* 查询和表格配置 */
const queryOpen = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  theaterId: props.theaterId || undefined,
});
const queryOptions = ref(queryConfig.items);
const getList = () => {
  proxy.$refs.tableRef.getList();
};
const multiple = ref(true);
const tableCols = ref(tableConfig.items);

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  form: {},
  readonly: false,
});
const formOptions = ref(formConfig.items);
// 重置
function reset() {
  dialogControl.value.form = {};
  // proxy.$refs["formRef"].formRef.resetFields()
}
// 取消
function cancel() {
  dialogControl.value.open = false;
  dialogControl.value.readonly = false;
  reset();
}

function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.souvenirBadge.remove, row);
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs["formRef"].formRef.validate((valid) => {
    if (valid) {
      proxy.$refs.tableRef.handleSubmit(
        proxy.$api.souvenirBadge.add,
        proxy.$api.souvenirBadge.update,
        dialogControl.value
      );
    }
  });
}
</script>
