
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" placeholder="票面名称" @search="getList">
      <template #btns>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增票面</el-button>
        </el-col>
      </template>
    </HandleRow>

    <!-- 表格区域 -->
    <CommonTable :listReq="$api.portfolio.portfolioInfoListByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="160" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">更换</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>

      <!-- 票面图预览 -->
      <template #commonImage="{ data }">
        <ImagePreview :src="data.scope.row.commonImage" :width="100" :height="60" :picNumMax="1" />
      </template>
    </CommonTable>

    <!-- 添加或修改票面对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
      <CommonForm v-model:form="dialogControl.form" :formOptions="formOptions" :readonly="dialogControl.readonly" ref="formRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Eticket">
import { formConfig, queryConfig, tableConfig } from './config'

const { proxy } = getCurrentInstance()

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  ticketType: 3
})
const queryOptions = computed(() => queryConfig.items)
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false
})
const formOptions = ref(proxy.deepClone(formConfig.items))

// 重置
function reset() {
  dialogControl.value.form = {}
}

// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}

function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '新增票面')
}

function handleUpdate(row) {
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.portfolio.portfolioInfoDetails, row, dialogControl.value, '更换票面')
}

function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.portfolio.portfolioInfoDelete, row)
}

function submitForm() {
  proxy.$refs["formRef"].formRef.validate(valid => {
    if (valid) {
      // 隐藏字段已经通过配置自动添加，无需手动设置
      console.log('🚀 ~ submitForm ~ 提交的表单数据:', dialogControl.value.form)

      proxy.$refs.tableRef.handleSubmit(proxy.$api.portfolio.portfolioInfoCreate, proxy.$api.portfolio.portfolioInfoUpdate, dialogControl.value)
    }
  })
}


    // onMounted(() => {
    //     //getList()
    // })
</script>

<style lang="scss" scoped>
</style>
