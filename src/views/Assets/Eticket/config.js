export const tableConfig = {
  items: [
    {
      label: "票面名称",
      prop: "name",
      type: "String",
      option: {},
    },
    {
      label: "票面图",
      prop: "commonImage",
      type: "Slot",
      option: {
        minWidth: 120,
      },
    },
    {
      label: "描述",
      prop: "introduction",
      type: "String",
      option: {},
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "String",
      option: {},
    },
  ],
};

export const formConfig = {
  items: [
    {
      label: "票面名称",
      prop: "name",
      type: "String",
      option: {},
      rules: [{ required: true, message: "请输入票面名称", trigger: "blur" }],
      size: "small",
    },
    {
      label: "票面图",
      prop: "commonImage",
      type: "Image",
      option: {
        limit: 1,
      },
      rules: [{ required: true, message: "请上传票面图", trigger: "change" }],
      size: "default",
    },
    {
      label: "描述",
      prop: "introduction",
      type: "String",
      option: {
        type: "textarea",
        rows: 3,
      },
      size: "default",
    },

    {
      prop: "ticketType",
      type: "Hidden",
      defaultValue: 3,
      description: "票券类型：3-电子票",
    },
    {
      prop: "lookStatus",
      type: "Hidden",
      defaultValue: 1,
      description: "查看状态：1-可查看",
    },
    {
      prop: "audit",
      type: "Hidden",
      defaultValue: 2,
      description: "审核状态：2-已审核",
    },
    {
      prop: "auditFlag",
      type: "Hidden",
      defaultValue: 1,
      description: "审核标志：1-已审核",
    },
    {
      prop: "seatStatus",
      type: "Hidden",
      defaultValue: 1,
      description: "座位状态：1-有座位",
    },
    {
      prop: "startTime",
      type: "Hidden",
      defaultValue: "2025-05-21 00:00:00",
      description: "开始时间",
    },
    {
      prop: "endTime",
      type: "Hidden",
      defaultValue: "2099-05-21 00:00:00",
      description: "结束时间",
    },
    // 动态隐藏字段示例 - 可以在 formatSubForm 中根据条件设置
    {
      prop: "submitTimestamp",
      type: "Hidden",
      description: "提交时间戳 - 动态生成",
    },
  ],
  size: "default",
};

export const queryConfig = {
  items: [
    {
      label: "票面名称",
      prop: "name",
      type: "String",
      option: {},
    },
  ],
};
