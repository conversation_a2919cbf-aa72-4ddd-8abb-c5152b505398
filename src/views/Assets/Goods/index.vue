<template>
  <div class="app-container">
    <SearchForm v-model:open="queryOpen" :options="queryOptions" v-model:query="queryParams" @search="getList" />

    <HandleRow v-model:query="queryParams" @search="getList" placeholder="剧目名称/关联剧场" v-if="!props.disableSearch">
      <template #btns>
        <!-- <el-col :span="1.5">
                    <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
                </el-col> -->
        <el-col :span="1.5">
          <el-button @click="queryOpen = true" icon="Filter" plain type="info">高级筛选</el-button>
        </el-col>
      </template>
    </HandleRow>

    <CommonTable :statusReq="handleSwitchEvent" :listReq="$api.portfolio.listByPage" :queryParams="queryParams" :tableCols="tableCols" v-model:multiple="multiple" ref="tableRef">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180" fixed="right">
        <template #default="scope">
          <!-- <BtnForCheck
            :req="$api.repertoireTicket.audit"
            :disabled="scope.row.audit"
            :rowId="scope.row.id"
            @change="getList"
            hasPassEvent
            @pass="goodsPass(scope.row)"
          /> -->

          <el-button :type="scope.row.audit ? 'warning' : 'success'" link @click="handleCheck(scope.row)">
            {{ scope.row.audit ? '修改' : '审核' }}
          </el-button>

          <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>

          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>

      <template #residue="{ data }">
        {{ data.scope.row.issuedQuantity - data.scope.row.getNum }}
        <!-- 11 -->
      </template>

      <template #ocrNo="{ data }">
        <!-- <el-button type="info" link size="default" @click="handleOcr">{{ data.scope.row.ocrNo || '暂无ocr' }}</el-button> -->
        <p class="ocrLink" :class="data.scope.row.ocrNo ? '' : 'infoColor'" @click="handleOcr(data.scope.row)">{{ data.scope.row.ocrNo || '暂无ocr' }}</p>
      </template>

      <template #digitalBill="{ data }">
        <div>
          <el-button v-if="data.scope.row.repertoireTicketCommonImage" type="primary" size="default" link @click="handleViewPic(data.scope.row.repertoireTicketCommonImage)">普通票</el-button>
          <el-button v-if="data.scope.row.coverFront" type="warning" size="default" link @click="handleViewPic([data.scope.row.coverFront, data.scope.row.coverReverse])">升级票</el-button>
        </div>
      </template>

      <template #digitalAvatar="{ data }">
        <div>
          <el-button v-if="data.scope.row.digitalAvatarCommonImage" type="primary" size="default" link @click="handleViewPic(data.scope.row.digitalAvatarCommonImage)">普通头像</el-button>
          <el-button v-if="data.scope.row.digitalAvatarImageList.length > 0" type="warning" size="default" link @click="handleViewPic(data.scope.row.digitalAvatarImageList)">升级头像</el-button>
        </div>
      </template>

      <template #scanningId="{ data }">
        <el-select v-model="data.scope.row.scanningId" placeholder="请选择" @change="(e) => handleChangeTemp(data.scope.row.id, e)">
          <el-option v-for="(item, index) in tempList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </template>
    </CommonTable>

    <!-- 添加或修改公告对话框 -->
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="dialogSize(formConfig.size)" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
      <CommonForm v-model:form="dialogControl.form" :formOptions="formOptions" :readonly="dialogControl.readonly" ref="formRef">
        <template #digitalBill>
          <div class="radioGroupCol" v-if="!dialogControl.readonly">
            <el-radio-group v-model="dialogControl.form.repertoireTicketId">
              <el-radio v-for="(item, index) in dialogControl.billList" :key="index" :label="item.id">
                <div class="billRow">
                  <div class="basicBill">
                    <div class="bill">
                      <el-image class="billImg" :src="baseUrl + item.commonImage" fit="cover"></el-image>
                      <div class="tag">普通</div>
                    </div>
                  </div>
                  <div class="highBill">
                    <div class="bill" v-if="item.coverFront">
                      <el-image class="billImg" :src="baseUrl + item.coverFront" fit="cover"></el-image>
                      <div class="tag">正面</div>
                    </div>
                    <div class="bill" v-if="item.coverReverse">
                      <el-image class="billImg" :src="baseUrl + item.coverReverse" fit="cover"></el-image>
                      <div class="tag">反面</div>
                    </div>
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </div>

          <div v-else>
            <div class="billRow">
              <div class="basicBill">
                <div class="thead">普通</div>
                <div class="bill">
                  <el-image class="billImg" :src="baseUrl + dialogControl.form.repertoireTicket.commonImage" fit="cover">
                    <template #error>
                      <div class="image-slot">
                        {{ dialogControl.form.repertoireTicket.commonImage ? '加载失败' : '暂无电子票' }}
                      </div>
                    </template>
                  </el-image>
                  <div class="tag">普通</div>
                </div>
              </div>
              <div class="highBill" v-if="dialogControl.form.repertoireTicket.coverFront || dialogControl.form.repertoireTicket.coverReverse">
                <div class="thead">升级</div>
                <div class="bill" v-if="dialogControl.form.repertoireTicket.coverFront">
                  <el-image class="billImg" :src="baseUrl + dialogControl.form.repertoireTicket.coverFront" fit="cover"></el-image>
                  <div class="tag">正面</div>
                </div>
                <div class="bill" v-if="dialogControl.form.repertoireTicket.coverReverse">
                  <el-image class="billImg" :src="baseUrl + dialogControl.form.repertoireTicket.coverReverse" fit="cover"></el-image>
                  <div class="tag">反面</div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <template #digitalAvatar>
          <div class="radioGroupCol" v-if="!dialogControl.readonly">
            <el-radio-group v-model="dialogControl.form.digitalAvatarId">
              <el-radio v-for="item in dialogControl.avatarList" :key="item.id" :label="item.id">
                <!-- {{item.label}} -->
                <div class="avatarRow">
                  <div class="basicAvatar">
                    <div class="avatar">
                      <el-image class="avatarImg" :src="baseUrl + item.digitalAvatarCommonImage" fit="fill"></el-image>
                      <div class="tag">普通</div>
                    </div>
                  </div>
                  <div class="highAvatar" v-if="item.digitalAvatarImageList?.length">
                    <div class="avatar" v-for="pic in item.digitalAvatarImageList" :key="pic.id">
                      <el-image class="avatarImg" :src="baseUrl + pic.image" fit="fill"></el-image>
                      <div class="tag">{{ pic.group + 1 }}</div>
                    </div>
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
          <div v-else>
            <div class="avatarRow">
              <div class="basicAvatar">
                <div class="thead">普通</div>
                <div class="avatar">
                  <el-image class="avatarImg" :src="baseUrl + dialogControl.form.digitalAvatarCommonImage" fit="fill">
                    <template #error>
                      <div class="image-slot">
                        {{ dialogControl.form.digitalAvatarCommonImage ? '加载失败' : '暂无头像' }}
                      </div>
                    </template>
                  </el-image>
                  <div class="tag">普通</div>
                </div>
              </div>
              <div class="highAvatar" v-if="dialogControl.form.digitalAvatarImageList?.length">
                <div class="thead">升级</div>
                <div class="avatar" v-for="pic in dialogControl.form.digitalAvatarImageList" :key="pic.id">
                  <el-image class="avatarImg" :src="baseUrl + pic.image" fit="fill"></el-image>
                  <div class="tag">{{ pic.group + 1 }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <template #price>
          <div style="display: flex; align-items: center">
            <svg-icon class-name="yuan" icon-class="yuan" v-if="dialogControl.form.free == 0 && dialogControl.form.price" style="flex-shrink: 0" />
            <div style="margin-left: 11px; min-width: 80px" v-if="!editPrice">
              {{ dialogControl.form.free ? '免费发放' : dialogControl.form.price }}
            </div>

            <div style="display: flex" v-else>
              <el-input v-model="dialogControl.form.price" class="priceInput" style="margin-left: 4px" v-if="!dialogControl.form.free"></el-input>
              <el-checkbox v-model="dialogControl.form.free" :true-label="1" :false-label="0" :indeterminate="false" @change="" style="margin-left: 10px">免费发放</el-checkbox>
            </div>

            <el-button style="padding: 4px 8px; margin-left: 12px" @click="editPrice = !editPrice" v-if="isCheck">
              <el-icon>
                <Edit v-if="!editPrice" />
                <Check v-else />
              </el-icon>
            </el-button>
          </div>
        </template>
      </CommonForm>

      <template #footer>
        <div class="dialog-footer">
          <div class="formItem2 readonlyFooter">
            <div class="label">
              <span>平台免责声明</span>
              <el-select v-model="dialogControl.form.statement" filterable style="margin-left: 10px" :disabled="!isCheck">
                <el-option :label="i.name" :value="i.id" v-for="(i, index) in portfolioStatementList" :key="index"></el-option>
              </el-select>
            </div>
            <div class="value">
              <div style="font-size: 14px; color: rgb(96, 98, 102)">
                {{ portfolioStatementTxt || '暂无声明' }}
              </div>
            </div>
          </div>

          <el-button v-if="isCheck && dialogControl.form.audit" type="primary" @click="handleUpdate">保 存</el-button>
          <div class="formItem btns" v-else-if="isCheck">
            <el-button v-if="dialogControl.readonly" type="success" @click="submitForm">通 过</el-button>
            <el-button v-if="dialogControl.readonly" type="danger" @click="handleReject">驳 回</el-button>
          </div>

          <!-- <el-button @click="cancel">取 消</el-button> -->

          <div class="formItem" style="margin-top: 18px" v-else>
            <div class="label">审核状态</div>
            <div class="value">
              <Audit :options="audit_flag" :value="dialogControl.form.audit" :text-tip="dialogControl.form.reasonsRejection" />
            </div>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 驳回理由 -->
    <el-dialog class="dialogWrap" :title="dialogControl2.title" v-model="dialogControl2.open" :width="dialogSize('small')" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef">
      <el-form :model="dialogControl2.form" ref="formRef2" :inline="false" size="normal">
        <el-form-item label="" :rules="rules" prop="reasonsRejection">
          <el-input v-model="dialogControl2.form.reasonsRejection" type="textarea" :rows="5" placeholder="请输入驳回理由"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm2">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>

    <!-- 应用Id -->
    <el-dialog class="dialogWrap" :title="dialogControl3.title" v-model="dialogControl3.open" :width="480" append-to-body destroy-on-close :close-on-click-modal="false" @closed="cancel" ref="dialogRef" draggable>
      <el-form :model="dialogControl3.form" ref="formRef3" :inline="false" size="normal">
        <el-form-item label="" :rules="rules" prop="ocrNo">
          <el-input v-model="dialogControl3.form.ocrNo" placeholder="请输入应用ID"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm3">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览 -->
    <el-image-viewer v-if="imgView.open" :urlList="imgView.images" :zIndex="5000" @close="() => (imgView.open = false)" />
  </div>
</template>

<script setup name="Goods">
import { tableConfig, formConfig, queryConfig } from './config'
import useUserStore from '@/store/modules/user'
import moment from 'moment'
import { isHttp } from '@/utils/validate.js'
import Audit from '@/components/CommonTable/audit.vue'

const pull = useUserStore().pull
const { proxy } = getCurrentInstance()
const { audit_flag } = proxy.useDict('audit_flag')
/* 传入的搜索条件值 */
const props = defineProps({
  repertoireId: { type: String, default: undefined },
  disableSearch: { type: Boolean, default: false },
})
const baseUrl = ref(import.meta.env.VITE_IMAGE)

/* 查询和表格配置 */
const queryOpen = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  ticketType: "1,2",
  repertoireId: props.repertoireId?.toString() || undefined,
})
const queryOptions = computed(() => {
  queryConfig.items.forEach((i) => {
    if (i.prop === 'audit') {
      i.option.pull = audit_flag.value
    }
  })
  return queryConfig.items
})
const getList = () => {
  proxy.$refs.tableRef.getList()
}
const multiple = ref(true)
const tableCols = ref(tableConfig.items)

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: '',
  form: {},
  readonly: false,
  billList: [],
  avatarList: [],
})
const formOptions = ref(proxy.deepClone(formConfig.items))
const editPrice = ref(false)
/* 免责声明列表 */
const portfolioStatementList = ref([])
const portfolioStatementTxt = computed(() => {
  let txt = ''
  portfolioStatementList.value.map((i) => {
    if (i.id == dialogControl.value.form.statement) txt = i.statement
  })
  return txt
})

const tempList = ref([])

onMounted(() => {
  handleGetList()
})

/* 获取模板列表 */
function handleGetList() {
  proxy.$api.scanTemplate.list({ pageNum: 1, pageSize: 999999 }).then((res) => {
    tempList.value = res.data.rows
  })
}
// 切换模板
function handleChangeTemp(id, scanningId) {
  proxy.$api.portfolio.update({ id, scanningId }).then((res) => {
    proxy.$modal.msgSuccess('已修改')

    getList()
  })
}

// 重置
function reset() {
  // console.log(proxy.$refs)
  dialogControl.value.form = {
    repertoireId: parseInt(props.repertoireId) || undefined,
  }
  editPrice.value = false
}
// 取消
function cancel() {
  dialogControl.value.open = false
  dialogControl.value.readonly = false
  reset()
}
function handleAdd() {
  reset()
  proxy.$refs.tableRef.handleOpenDialog(dialogControl.value, '添加组合商品')
}
function handleView(row) {
  isCheck.value = false
  dialogControl.value.readonly = true
  proxy.$refs.tableRef.handleGetDetail(proxy.$api.portfolio.detail, row, dialogControl.value, '查看详情')
}
function handleUpdate(row) {
  // proxy.$refs.tableRef.handleGetDetail(proxy.$api.portfolio.detail, row, dialogControl.value, '修改组合商品')

  if (dialogControl.value.form.free) {
    dialogControl.value.form.price = 0
  }
  dialogControl.value.form.portfolioStatementId = dialogControl.value.form.statement
  dialogControl.value.form.statement = portfolioStatementTxt.value

  proxy.$refs.tableRef.handleSubmit(proxy.$api.portfolio.add, proxy.$api.portfolio.update, dialogControl.value)
}
function handleDelete(row) {
  proxy.$refs.tableRef.handleDelete(proxy.$api.portfolio.remove, row)
}
function submitForm() {
  // console.log(proxy.$refs["formRef"].formRef.validate)
  proxy.$refs['formRef'].formRef.validate((valid) => {
    if (valid) {
      proxy.$api.portfolio
        .audit({
          id: dialogControl.value.form.id,
          price: dialogControl.value.form.free ? 0 : dialogControl.value.form.price,
          portfolioStatementId: dialogControl.value.form.statement,
          statement: portfolioStatementTxt.value,
          free: dialogControl.value.form.free,
          issuedQuantity: dialogControl.value.form.issuedQuantity,
          audit: '2',
        })
        .then((res) => {
          proxy.$modal.msgSuccess('已通过')
          dialogControl.value.open = false
          getList()
        })
    }
  })
}
/* 审核驳回 */
const dialogControl2 = ref({
  title: '驳回理由',
  open: false,
  form: {
    id: '',
    audit: '1',
    reasonsRejection: '',
  },
})
function handleReject() {
  dialogControl2.value.form.reasonsRejection = ''
  dialogControl2.value.open = true
}
function submitForm2() {
  proxy.$refs['formRef2'].validate((valid) => {
    if (valid) {
      dialogControl2.value.form.audit = '1'
      dialogControl2.value.form.id = dialogControl.value.form.id
      proxy.$api.portfolio.audit(dialogControl2.value.form).then((res) => {
        proxy.$modal.msgSuccess('已驳回')
        dialogControl2.value.open = false
        dialogControl.value.open = false
        getList()
      })
    }
  })
}

/* 应用ID修改 */
const dialogControl3 = ref({
  title: '应用ID',
  open: false,
  form: {
    id: '',
    ocrNo: '',
  },
})
function handleOcr(row) {
  dialogControl3.value.form.ocrNo = row.ocrNo || ''
  dialogControl3.value.form.id = row.id
  dialogControl3.value.open = true
}
function submitForm3() {
  proxy.$refs['formRef3'].validate((valid) => {
    if (valid) {
      proxy.$api.portfolio.update(dialogControl3.value.form).then((res) => {
        proxy.$modal.msgSuccess('已修改')
        dialogControl3.value.open = false
        getList()
      })
    }
  })
}

function handleSwitchEvent(id, type) {
  if (type === 'seatStatus')
    $api.portfolio.updateSeatStatus(id)
  else if (type === 'compositeRepertoireName') {

  }

}

/* 监听下拉 */
// watch(
//   () => dialogControl.value.form.repertoireId,
//   (val) => {
//     if (val) {
//       proxy.$api.repertoireTicket.pull({ id: val }).then((res) => {
//         dialogControl.value.billList = [...res.data];
//       });
//       proxy.$api.digitalAvatar.pull({ id: val }).then((res) => {
//         dialogControl.value.avatarList = [...res.data];
//       });
//     } else {
//       dialogControl.value.billList = [];
//       dialogControl.value.form.repertoireTicketId = undefined;
//       dialogControl.value.avatarList = [];
//       dialogControl.value.form.digitalAvatarId = undefined;
//     }
//   }
// );

// watch(()=>dialogControl.value.form)

const imgView = ref({
  open: false,
  images: [],
})
function handleViewPic(url) {
  if (url && typeof url === 'string') {
    let urls = url.split(',')
    imgView.value.images = urls.map((i) => {
      if (!isHttp(i)) i = baseUrl.value + i
      return i
    })
    imgView.value.open = true
    return
  } else if (url && Array.isArray(url)) {
    let urls = url.map((i) => i.image || i)
    imgView.value.images = urls.map((i) => {
      if (!isHttp(i)) i = baseUrl.value + i
      return i
    })
    imgView.value.open = true
    return
  }
  proxy.$modal.msgError('打开失败；' + url)
  return
}
const isCheck = ref(false)
async function handleCheck(row) {
  await getPortfolioStatement()

  isCheck.value = true
  dialogControl.value.readonly = true

  proxy.$refs.tableRef.handleGetDetail(proxy.$api.portfolio.detail, row, dialogControl.value, '审核定价', (res) => {
    if (!res.data.statement) {
      if (!res.data.repertoireTicket.coverFront && !res.data.repertoireTicket.coverReverse) {
        /* 普通票 */
        portfolioStatementList.value.map((i) => {
          if (i.type == 1) res.data.statement = i.id
        })
      } else if (res.data.repertoireTicket.coverFront && res.data.repertoireTicket.coverReverse) {
        /* 升级票 */
        portfolioStatementList.value.map((i) => {
          if (i.type == 2) res.data.statement = i.id
        })
      }
    } else {
      res.data.statement = res.data.portfolioStatementId
    }

    if (res.data.updateCause) formOptions.value[10].option.isHide = false
  })
}

/* 免责声明 */
async function getPortfolioStatement() {
  await proxy.$api.portfolioStatement.detail({ pageNum: 1, pageSize: 1000 }).then((res) => {
    portfolioStatementList.value = res.data.rows
  })
}
</script>

<style lang="scss" scoped>
.priceInput {
  &:deep(.el-input__wrapper) {
    border: none;
    outline: none;
    box-shadow: none;
    border-bottom: 1px solid #ddd;
    border-radius: 0px;
    max-width: 80px;
  }
}

.ocrLink {
  color: var(--el-color-warning);
  text-decoration: underline;
  cursor: pointer;
  &.infoColor {
    color: var(--el-color-info);
  }
}
</style>
