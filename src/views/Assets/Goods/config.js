import useUserStore from "@/store/modules/user";
import { useDict } from "@/utils/dict";
const pull = useUserStore().pull;

const { audit_flag } = useDict("audit_flag");

export const tableConfig = {
  items: [
    // { label: '编码', prop: 'code', type: 'string', option: {} },
    {
      label: "剧目名称",
      prop: "repertoireName",
      type: "String",
      option: {
        fixed: "left",
      },
    },
    { label: "关联剧场", prop: "theaterName", type: "String", option: {} },
    { label: "批次", prop: "batch", type: "String", option: {} },
    {
      label: "电子票根版式",
      prop: "digitalBill",
      type: "Slot",
      option: {
        minWidth: 160,
      },
    },
    {
      label: "数字头像",
      prop: "digitalAvatar",
      type: "Slot",
      option: {
        minWidth: 160,
      },
    },

    // { label: '发行方', prop: 'issuerName', type: 'String', option: {} },
    { label: "发行数量", prop: "issuedQuantity", type: "String", option: {} },
    {
      label: "发行时间",
      prop: "startTime",
      type: "issueTime",
      option: {
        minWidth: 300,
      },
    },
    { label: "商品价格", prop: "price", type: "Price", option: {} },
    {
      label: "审核状态",
      prop: "audit",
      type: "Audit",
      option: {
        pull: audit_flag,
        tip: "reasonsRejection",
      },
    },
    {
      label: "已领取",
      prop: "getNum",
      type: "receivedNumber",
      option: {
        type: 1,
      },
    },
    // { label: "领取剩余", prop: "residue", type: "Slot", option: {} },
    {
      label: "升级数量",
      prop: "upgradeNum",
      type: "receivedNumber",
      option: {
        type: 1,
        upgrade: true,
      },
    },
    {
      label: "上架/售罄",
      prop: "hasSold",
      type: "hasSold",
      option: {},
    },
    // {
    //   label: '应用ID',
    //   prop: 'ocrNo',
    //   type: 'Slot',
    //   option: {
    //     minWidth: 120,
    //   },
    // },
    {
      label: "识别模板",
      prop: "scanningId",
      type: "Slot",
      option: {
        minWidth: 200,
      },
    },
    {
      label: "座位唯一",
      prop: "seatStatus",
      type: "Switch",
      option: {
        actionOnName: "开启",
        actionOffName: "关闭",
      },
    },
    {
      label: "电子票中间是否合成剧名",
      prop: "ifCompositeRepertoireName",
      type: "Switch",
      option: {
        actionOnName: "开启",
        actionOffName: "关闭",
      },
    },
  ],
};

export const formConfig = {
  items: [
    {
      label: "剧目名称",
      prop: "repertoireName",
      type: "String",
      option: {},
      rules: [{ required: true, message: "请选择剧目名称", trigger: "" }],
      size: "small",
    },
    {
      label: "剧场名称",
      prop: "theaterName",
      type: "String",
      option: {},
      rules: [{ required: true, message: "请选择剧场名称", trigger: "" }],
      size: "small",
    },
    {
      label: "组合名称",
      prop: "name",
      type: "Name",
      option: {},
      rules: [{ required: true, message: "请输入组合名称", trigger: "" }],
      size: "small",
    },

    {
      label: "选择电子票根",
      prop: "digitalBill",
      type: "Slot",
      option: {},
      size: "default",
      position: "top",
    },
    {
      label: "选择数字头像",
      prop: "digitalAvatar",
      type: "Slot",
      option: {},
      size: "default",
      position: "top",
    },

    {
      label: "组合介绍",
      prop: "introduction",
      type: "String",
      option: {
        type: "textarea",
      },
      size: "default",
    },

    {
      label: "发行数量",
      prop: "issuedQuantity",
      type: "Number",
      option: {},
      rules: [{ required: true, message: "请输入发行数量", trigger: "" }],
      size: "default",
    },
    {
      label: "发行时间",
      prop: "startTime",
      type: "Date",
      option: {},
      rules: [{ required: true, message: "请输入发行时间", trigger: "" }],
      size: "small",
    },
    {
      label: "结束时间",
      prop: "endTime",
      type: "Date",
      option: {},
      rules: [{ required: true, message: "请输入结束时间", trigger: "" }],
      size: "small",
    },
    {
      label: "设置组合价格",
      prop: "price",
      type: "Slot",
      option: {},
      rules: [{ required: true, message: "请输入组合价格", trigger: "" }],
      size: "small",
    },
    {
      label: "修改原因",
      prop: "updateCause",
      type: "String",
      option: {
        type: "textarea",
        isHide: true,
      },
      rules: [{ required: true, message: "请输入修改原因", trigger: "" }],
      size: "default",
    },
  ],
  size: "default",
};

export const queryConfig = {
  items: [
    {
      label: "商家名称",
      prop: "merchantId",
      type: "multipleSelect",
      option: {
        pull: pull.merchant,
      },
    },
    {
      label: "审核状态",
      prop: "audit",
      type: "tags",
      option: {
        pull: audit_flag,
      },
    },
    {
      label: "发行时间",
      prop: "time",
      type: "daterange",
      option: {},
    },
  ],
};
