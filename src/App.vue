<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
</script>

<style lang="scss">
.dialogWrap {
  max-height: 89vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .el-dialog__body {
    padding-top: 10px;
    height: fit-content;
    max-height: calc(89vh - 74px);
    overflow: auto;
    // overflow: hidden;
    & > .app-container {
      padding: 0;
      height: calc(89vh - 114px);
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    .el-button {
      width: 200px;
    }

    .el-form-item__content {
      width: fit-content;
    }
  }
  .radioGroupCol {
    .el-radio-group {
      flex-direction: column;
      .el-radio {
        margin: 0;
        height: fit-content;
        width: 100%;
        .el-radio__label {
          margin-left: 10px;
        }
        // align-items: flex-start;
      }
    }
  }
  .avatarRow {
    display: flex;
    margin: 5px 0;
    .avatar {
      width: 80px;
      height: 80px;
      margin: 0 10px 10px 0;
      position: relative;
      .avatarImg {
        width: 80px;
        height: 80px;
      }
      .tag {
        position: absolute;
        top: 4px;
        left: auto;
        right: 4px;
        color: #fff;
        font-size: 6px;
        line-height: 14px;
        padding: 0 3px;
        border-radius: 4px;
        background-color: #342b54;
      }
    }
    .basicAvatar {
      margin-right: 30px;
    }
    .thead {
      width: 100%;
      text-align: center;
      line-height: 40px;
      font-weight: 600;
      font-size: 16px;
    }
    .highAvatar {
      display: flex;
      flex-wrap: wrap;
      min-width: 80px;
      .thead {
        width: calc(100% - 32px);
      }

      .avatar {
        &:last-child {
          margin-right: 0;
        }
        .tag {
          background-color: #ffd8a8;
        }
      }
    }
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      font-size: 12px;
    }
  }
  .billRow {
    display: flex;
    margin: 5px 0;
    .bill {
      width: 169px;
      height: 89px;
      margin-right: 10px;
      position: relative;
      .billImg {
        width: 169px;
        height: 89px;
      }
      .tag {
        position: absolute;
        top: 4px;
        left: auto;
        right: 4px;
        color: #fff;
        font-size: 12px;
        line-height: 18px;
        padding: 0 4px;
        border-radius: 4px;
        background-color: #342b54;
      }
    }
    .basicBill {
      margin-right: 30px;
    }
    .thead {
      width: 100%;
      text-align: center;
      line-height: 40px;
      font-weight: 600;
      font-size: 16px;
    }
    .highBill {
      display: flex;
      flex-wrap: wrap;
      width: 350px;
      .bill {
        &:last-child {
          margin-right: 0;
        }
        .tag {
          background-color: #ffd8a8;
        }
      }
    }
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      font-size: 12px;
    }
  }

  .readonlyFooter {
    width: 100%;
    border-top: 1px solid #bbb;
    padding-top: 8px;
  }
  .formItem {
    width: 100%;
    display: flex;
    text-align: left;
    .label {
      width: 7em;
      height: 32px;
      line-height: 32px;
      display: inline-flex;
      justify-content: flex-end;
      align-items: flex-start;
      flex: 0 0 auto;
      font-size: 14px;
      color: #606266;
      font-weight: 700;
      height: 32px;
      line-height: 32px;
      padding: 0 12px 0 0;
      box-sizing: border-box;
    }
    .value {
      line-height: 32px;
    }
  }
  .formItem2 {
    width: 100%;
    text-align: left;
    .label {
      min-width: 7em;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      color: #606266;
      font-weight: 700;
      line-height: 32px;
      padding: 0 12px 0 0;
      box-sizing: border-box;
      width: 100%;
    }
    .value {
      padding-left: 42px;
      margin-top: 20px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

.el-popper {
  max-width: 500px;
}

.el-select-v2__popper {
  max-width: initial;

  .el-select-dropdown__list {
    width: 600px !important;
  }
}

.tooltipH {
  span:first-child {
    display: block;
    max-height: 300px;
    overflow: auto;
  }
}
</style>
