import { login, logout, getInfo } from '@/api/login'
import { pull as pull1 } from '@/api/all/merchant'
import { pull as pull2 } from '@/api/all/repertoire'
import { pull as pull3 } from '@/api/all/theater'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/assets/logo/logo.png'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      name: '',
      avatar: '',
      lastLoginTime:'',
      roles: [],
      permissions: [],
      pull:{
        merchant:[],
        merchantRepPull: [],
        merchantThePull: [],
        repertoire:[],
        theater:[],
      }
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            setToken(res.token)
            this.token = res.token
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(async (res) => {
            const user = res.user
            const avatar = (user.avatar == "" || user.avatar == null) ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;

            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.name = user.userName
            this.avatar = avatar;
            this.lastLoginTime = user.lastLoginTime || '暂未获取时间'
            await this.getPull()
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      async getPull(){
        await pull1({

        }).then(res => {
          // console.log('merchantPull', res)
          this.pull.merchant = res.data.map(i =>({
            value:i.id,
            label:i.name,
            merchantId:i.merchantId,
          }))
        }),
        await pull1({
          merchantCategory:1
        }).then(res => {
          // console.log('merchantRepPull', res)
          this.pull.merchantRepPull = res.data.map(i => ({
            value:i.id,
            label:i.name,
            merchantId:i.merchantId,
          }))
        }),
        await pull1({
          merchantCategory:2
        }).then(res => {
          // console.log('merchantThePull', res)
          this.pull.merchantThePull = res.data.map(i => ({
            value:i.id,
            label:i.name,
            merchantId:i.merchantId,
          }))
        }),
        await pull2({
          audit:2
        }).then(res => {
          // console.log('repertoirePull', res)
          this.pull.repertoire = res.data.map(i =>({
            value:i.id,
            label:i.name,
            merchantId:i.merchantId,
          }))
        })
        await pull3({
          audit:2
        }).then(res => {
          // console.log('theaterPull', res)
          this.pull.theater = res.data.map(i =>({
            value:i.id,
            label:i.name,
            merchantId:i.merchantId,
          }))
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            removeToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
