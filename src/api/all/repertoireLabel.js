import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/repertoireLabel/listByPage', data)
}

export const list = (data) => {
    return api.get('/repertoireLabel/list', data)
}
export const listByRepertoireId = (data) => {
    return api.get('/repertoireLabel/listByRepertoireId', data)
}

export const detail = (data) => {
    return api.get('/repertoireLabel/details', data)
}

export const add = (data) => {
    return api.post('/repertoireLabel/add', data)
}

export const update = (data) => {
    return api.put('/repertoireLabel/update', data)
}

export const remove = (data) => {
    return api.del('/repertoireLabel/delete', data)
}