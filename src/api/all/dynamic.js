import * as api from "../commonApi.js";

export const listByPage = (data) => {
  return api.post("/dynamic/listByPage", data);
};

export const detail = (data) => {
  return api.get("/dynamic/details", data);
};

export const add = (data) => {
  return api.post("/dynamic/add", data);
};

export const update = (data) => {
  return api.put("/dynamic/update", data);
};

export const remove = (data) => {
  return api.del("/dynamic/delete", data);
};

export const updateStatus = (data) => {
  return api.put(`/dynamic/updateStatus/${data}`);
};
