import * as api from "../commonApi.js";

export const listByPage = (data) => {
  return api.post("/portfolio/listByPage", data);
};

export const detail = (data) => {
  return api.get("/portfolio/details", data);
};

export const add = (data) => {
  return api.post("/portfolio/add", data);
};

export const update = (data) => {
  return api.put("/portfolio/update", data);
};

export const audit = (data) => {
  return api.put("/portfolio/audit", data);
};

export const remove = (data) => {
  return api.del("/portfolio/delete", data);
};

export const updateSeatStatus = (id) => {
  return api.put(`/portfolio/updateSeatStatus/${id}`);
};

export const portfolioInfoListByPage = (data) => {
  return api.post("/portfolio/portfolioInfoListByPage", data);
};

export const portfolioInfoDetails = (data) => {
  return api.get("/portfolio/portfolioInfoDetails", data);
};

export const portfolioInfoUpdate = (data) => {
  return api.put("/portfolio/portfolioInfoUpdate", data);
};

export const portfolioInfoDelete = (data) => {
  return api.del("/portfolio/portfolioInfoDelete", data);
};

/**
 * 增加
 * @param {*} data
 * @returns
 */
export const portfolioInfoCreate = (data) => {
  return api.post("/portfolio/portfolioInfoCreate", data);
};
