import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/merchant/listByPage', data)
}

export const detail = (data) => {
    return api.get('/merchant/details', data)
}

export const add = (data) => {
    return api.post('/merchant/add', data)
}

export const update = (data) => {
    return api.put('/merchant/update', data)
}

export const updatePwd = (data) => {
    return api.put('/merchantUser/updatePwd',data)
}

export const pull = (data) => {
    return api.post('/merchant/pull', data)
}

export const remove = (data) => {
    return api.del('/merchant/delete', data)
}

export const findMerchantAddCount = (data) => {
    return api.post('/merchant/findMerchantAddCount', data)
}