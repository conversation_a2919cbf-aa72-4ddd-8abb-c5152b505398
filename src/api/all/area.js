import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/area/listByPage', data)
}
export const findAreaTree = (data) => {
    return api.get('/area/findAreaTree', data)
}
export const detail = (data) => {
    return api.get('/area/details', data)
}

export const add = (data) => {
    return api.post('/area/add', data)
}

export const update = (data) => {
    return api.put('/area/update', data)
}

export const remove = (data) => {
    return api.del('/area/delete', data)
}