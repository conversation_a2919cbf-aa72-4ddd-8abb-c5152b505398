import request from "@/utils/request";

import * as api from "../commonApi.js";

export const listByPage = (data) => {
  return api.post("/autoReply/listByPage", data);
};

export const detail = (data) => {
  return api.get("/autoReply/details", data);
};

export const add = (data) => {
  return api.post("/autoReply/add", data);
};

export const update = (data) => {
  return api.put("/autoReply/update", data);
};

export const remove = (data) => {
  return api.del("/autoReply/delete", data);
};
