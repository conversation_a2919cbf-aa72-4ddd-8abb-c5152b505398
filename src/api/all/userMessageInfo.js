import * as api from '../commonApi.js'

export const listByPage = (data) => {
  return api.post('/userMessageInfo/listByPage', data)
}

export const detail = (data) => {
  return api.get('/userMessageInfo/details', data)
}

export const add = (data) => {
  return api.post('/userMessageInfo/add', data)
}

export const update = (data) => {
  return api.put('/userMessageInfo/update', data)
}

export const remove = (data) => {
  return api.del('/userMessageInfo/delete', data)
}
