import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
  return api.post('/userReceivingRecords/listByPage', data)
}

export const detail = (data) => {
  return api.get('/userReceivingRecords/details', data)
}

export const findCollectionRecipient = (data) => {
  return api.post('/userReceivingRecords/findCollectionRecipient', data)
}

export const findUserReceivingRecordsAddCount = (data) => {
  return api.post('/userReceivingRecords/findUserReceivingRecordsAddCount', data)
}

export const updateSeat = (data) => {
  return api.post('/userReceivingRecords/updateSeat', data)
}
