import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/rankMedal/listByPage', data)
}

export const detail = (data) => {
    return api.get('/rankMedal/details', data)
}

export const audit = (data) => {
    return api.put('/rankMedal/audit', data)
}

export const remove = (data) => {
    return api.del('/rankMedal/delete', data)
}
