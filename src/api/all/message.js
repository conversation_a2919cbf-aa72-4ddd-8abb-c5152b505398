import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/message/listByPage', data)
}

export const detail = (data) => {
    return api.get('/message/details', data)
}

export const add = (data) => {
    return api.post('/message/add', data)
}

export const update = (data) => {
    return api.put('/message/update', data)
}

export const pull = (data) => {
    return api.post('/message/pull', data)
}

export const remove = (data) => {
    return api.del('/message/delete', data)
}