import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/userMessage/listByPage', data)
}

export const detail = (data) => {
    return api.get('/userMessage/details', data)
}

export const add = (data) => {
    return api.post('/userMessage/add', data)
}

export const update = (data) => {
    return api.put('/userMessage/update', data)
}


export const remove = (data) => {
    return api.del('/userMessage/delete', data)
}