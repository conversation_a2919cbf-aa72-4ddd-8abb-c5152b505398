import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
  return api.post('/comment/listByPage', data)
}

export const detail = (data) => {
  return api.get('/comment/details', data)
}

export const report = (id) => {
  return api.put(`/comment/report/${id}`)
}

export const add = (data) => {
  return api.post('/comment/add', data)
}

export const update = (data) => {
  return api.put('/comment/update', data)
}

export const remove = (data) => {
  return api.del('/comment/delete', data)
}
