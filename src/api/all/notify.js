import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/notify/listByPage', data)
}

export const detail = (data) => {
    return api.get('/notify/details', data)
}

export const add = (data) => {
    return api.post('/notify/add', data)
}

export const update = (data) => {
    return api.put('/notify/update', data)
}

export const remove = (data) => {
    return api.del('/notify/delete', data)
}