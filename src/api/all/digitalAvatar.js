import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/digitalAvatar/listByPage', data)
}

export const detail = (data) => {
    return api.get('/digitalAvatar/details', data)
}

export const audit = (data) => {
    return api.put('/digitalAvatar/audit', data)
}

export const remove = (data) => {
    return api.del('/digitalAvatar/delete', data)
}