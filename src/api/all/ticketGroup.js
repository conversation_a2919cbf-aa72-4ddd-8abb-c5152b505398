import * as api from '../commonApi.js'

/* 查询电子票分组 */
export const listByPage = (data) => {
  return api.post('/ticketGroup/listByPage', data)
}

/* 查询电子票分组详情 */
export const detail = (data) => {
  return api.get('/ticketGroup/details', data)
}

/* 添加电子票分组 */
export const add = (data) => {
  return api.post('/ticketGroup/add', data)
}

/* 修改电子票分组 */
export const update = (data) => {
  return api.put('/ticketGroup/update', data)
}

/* 电子票分组状态 */
export const updateStatus = (id) => {
  return api.put(`/ticketGroup/updateStatus/${id}`)
}

/* 删除电子票分组 */
export const remove = (data) => {
  return api.del('/ticketGroup/delete', data)
}
