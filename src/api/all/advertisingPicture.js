import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/advertisingPicture/listByPage', data)
}

export const detail = (data) => {
    return api.get('/advertisingPicture/details', data)
}
export const add = (data) => {
    return api.post('/advertisingPicture/add', data)
}
export const update = (data) => {
    return api.put('/advertisingPicture/update', data)
}
export const updateStatus = (data) => {
    return api.put(`/advertisingPicture/updateStatus/${data}`)
}

export const remove = (data) => {
    return api.del('/advertisingPicture/delete', data)
}