import * as api from '../commonApi.js'

/* 查询榜单列(分页) */
export const listByPage = (data) => {
  return api.post('/leaderboard/listByPage', data)
}

/* 查询榜单详情 */
export const detail = (data) => {
  return api.get('/leaderboard/details', data)
}

/* 添加榜单 */
export const add = (data) => {
  return api.post('/leaderboard/add', data)
}

/* 修改榜单 */
export const update = (data) => {
  return api.put('/leaderboard/update', data)
}

/* 修改榜单状态 */
export const updateStatus = (id) => {
  return api.put(`/leaderboard/updateStatus/${id}`)
}

/* 删除榜单 */
export const remove = (data) => {
  return api.del('/leaderboard/delete', data)
}

/* 榜单剧目统计 */
export const repertoireLeaderboard = (data) => {
  return api.post('/leaderboard/findRepertoireLeaderboard', data)
}
