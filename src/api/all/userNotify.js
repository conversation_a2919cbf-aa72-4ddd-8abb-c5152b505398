import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/userNotify/listByPage', data)
}

export const detail = (data) => {
    return api.get('/userNotify/details', data)
}

export const add = (data) => {
    return api.post('/userNotify/add', data)
}

export const update = (data) => {
    return api.put('/userNotify/update', data)
}

export const remove = (data) => {
    return api.del('/userNotify/delete', data)
}