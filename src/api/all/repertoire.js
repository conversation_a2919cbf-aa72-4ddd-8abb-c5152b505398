import * as api from "../commonApi.js";

export const listByPage = (data) => {
  return api.post("/repertoire/listByPage", data);
};

export const findRepertoireInfo = (data) => {
  return api.get("/repertoire/findRepertoireInfo", data);
};

export const detail = (data) => {
  return api.get("/repertoire/details", data);
};

export const updateLabel = (data) => {
  return api.put(`/repertoire/updateLabel`, data);
};

export const updateRecommend = (data) => {
  return api.put(`/repertoire/updateRecommend/${data}`);
};

export const audit = (data) => {
  return api.put(`/repertoire/audit`, data);
};

export const remove = (data) => {
  return api.del("/repertoire/delete", data);
};

export const pull = (data) => {
  return api.post("/repertoire/pull", data);
};

export const findRepertoireAddCount = (data) => {
  return api.post("/repertoire/findRepertoireAddCount", data);
};

export const updateStatus = (id) => {
  return;
  api.put(`/repertoire/updateStatus/${id}`);
};
/**
 * 更新剧目
 * @param {Object} data - 更新数据
 * @param {Object} config - 请求配置
 * @returns
 */
export const update = (data, config = {}) => {
  return api.put(`/repertoire/update`, data, config);
};
