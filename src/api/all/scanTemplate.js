import * as api from '../commonApi.js'

export const list = (data) => {
  return api.post('/scanning/listByPage', data)
}

export const detail = (data) => {
  return api.get('/scanning/details', data)
}

export const add = (data) => {
  return api.post('/scanning/add', data)
}

export const update = (data) => {
  return api.put('/scanning/update', data)
}

export const updateStatus = (data) => {
  return api.put(`/scanning/updateStatus/${data}`)
}

export const remove = (data) => {
  return api.del(`/scanning/delete`, data)
}
