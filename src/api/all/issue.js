import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/issue/listByPage', data)
}

export const detail = (data) => {
    return api.get('/issue/details', data)
}

export const report = (id) => {
    return api.put(`/issue/report/${id}`)
}

export const add = (data) => {
    return api.post('/issue/add', data)
}

export const update = (data) => {
    return api.put('/issue/update', data)
}

export const remove = (data) => {
    return api.del('/issue/delete', data)
}