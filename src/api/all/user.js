import request from '@/utils/request'

import * as api from '../commonApi.js'

export const listByPage = (data) => {
    return api.post('/user/listByPage', data)
}

export const detail = (data) => {
    return api.get('/user/details', data)
}

export const add = (data) => {
    return api.post('/user/add', data)
}

export const update = (data) => {
    return api.put('/user/update', data)
}

export const updateStatus = (id) => {
    return api.put(`/user/updateStatus/${id}`)
}

export const resetPwd = (data) => {
    return api.put(`/user/resetPwd`, data)
}

export const updateSpeakStatus = (id) => {
    return api.put(`/user/updateSpeakStatus/${id}`)
}

export const remove = (data) => {
    return api.del('/user/delete', data)
}

export const findUserSexCount = (data) => {
    return api.post('/user/findUserSexCount', data)
}
