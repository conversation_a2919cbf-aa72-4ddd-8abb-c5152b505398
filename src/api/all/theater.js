import * as api from "../commonApi.js";

export const listByPage = (data) => {
  return api.post("/theater/listByPage", data);
};

export const findTheaterInfo = (data) => {
  return api.get("/theater/findTheaterInfo", data);
};

export const detail = (data) => {
  return api.get("/theater/details", data);
};

export const updateRecommend = (data) => {
  return api.put(`/theater/updateRecommend/${data}`);
};

export const audit = (data) => {
  return api.put(`/theater/audit`, data);
};

export const remove = (data) => {
  return api.del("/theater/delete", data);
};

export const pull = (data) => {
  return api.post("/theater/pull", data);
};

export const findTheaterAddCount = (data) => {
  return api.post("/theater/findTheaterAddCount", data);
};

export const updateStatus = (id) => {
  return api.put(`/theater/updateStatus/${id}`);
};

/**
 * 更新剧场
 * @param {Object} data - 更新数据
 * @param {Object} config - 请求配置
 * @returns
 */
export const update = (data, config = {}) => {
  return api.put(`/theater/update`, data, config);
};
