import request from '@/utils/request'
import img from '@/assets/images/profile.jpg'
// 查询列表
export function list(query) {
    return new Promise((resolve, reject) => {
        let res = {
            data: {
                rows: [
                    {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    }, {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    }, {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    }, {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    }, {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    }, {
                        id: 1,
                        name: 'name1',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img,
                        title: '轮播图',
                        status: 1,
                    },
                    {
                        id: 2,
                        name: 'name2',
                        time: '2022-12-12 08:24:00',
                        createTime: '2022-12-12 08:24:00',
                        image: img
                    }
                ],
                total: 2
            },
            code: 200
        }
        console.log('listRequest', query)
        resolve(res)
    })
}
export function detail(query) {
    return new Promise((resolve, reject) => {
        let res = {
            data: {
                id: 1,
                name: 'name',
                img:img
            }
        }
        console.log('detailRequest')
        resolve(res)
    })
}
export function add(query) {
    return new Promise((resolve, reject) => {
        let res = {
            data: 3,
            code: 200
        }
        console.log('addRequest')
        resolve(res)
    })
}
export function update(query) {
    return new Promise((resolve, reject) => {
        let res = {
            data: 2,
            code: 200
        }
        console.log('updateRequest')
        resolve(res)
    })
}
export function remove(query) {
    return new Promise((resolve, reject) => {
        let res = {
            data: 1,
            code: 200
        }
        console.log('removeRequest')
        resolve(res)
    })
}