import request from '@/utils/request'

// 获取路由
export const getRouters = () => {
  return request({
    url: '/getRouters',
    method: 'get'
  })
  // return new Promise((resolve, reject) => {
  //   let res = {
  //     data: [
  //       {
  //         name: 'Store',
  //         path: '/Store',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '商家信息管理',
  //           icon: 'menu-store',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Store',
  //             path: 'store',
  //             hidden: false,
  //             component: 'Store/index',
  //             meta: {
  //               title: '商家信息管理',
  //               icon: 'menu-store',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Theater',
  //         path: '/Theater',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '剧场信息管理',
  //           icon: 'menu-theater',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Theater',
  //             path: 'theater',
  //             hidden: false,
  //             component: 'Theater/index',
  //             meta: {
  //               title: '剧场信息管理',
  //               icon: 'menu-theater',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Play',
  //         path: '/Play',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '剧目信息管理',
  //           icon: 'menu-play',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Play',
  //             path: 'play',
  //             hidden: false,
  //             component: 'Play/index',
  //             meta: {
  //               title: '剧目信息管理',
  //               icon: 'menu-play',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Session',
  //         path: '/Session',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '演出场次管理',
  //           icon: 'menu-session',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Session',
  //             path: 'session',
  //             hidden: false,
  //             component: 'Session/index',
  //             meta: {
  //               title: '演出场次管理',
  //               icon: 'menu-session',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Assets',
  //         path: '/Assets',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: true,
  //         meta: {
  //           title: '数字资产管理',
  //           icon: 'menu-assets',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Bill',
  //             path: 'bill',
  //             hidden: false,
  //             component: 'Assets/Bill/index',
  //             meta: {
  //               title: '电子票管理',
  //               icon: 'menu-bill',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Avatar',
  //             path: 'avatar',
  //             hidden: false,
  //             component: 'Assets/Avatar/index',
  //             meta: {
  //               title: '数字头像管理',
  //               icon: 'menu-avatar',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Emblem',
  //             path: 'emblem',
  //             hidden: false,
  //             component: 'Assets/Emblem/index',
  //             meta: {
  //               title: '纪念徽章管理',
  //               icon: 'menu-emblem',
  //               noCache: false,
  //               link: null,
  //             },
  //           },    

  //         ]
  //       },
  //       {
  //         name: 'Medal',
  //         path: '/Medal',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '等级勋章管理',
  //           icon: 'menu-medal',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Medal',
  //             path: 'medal',
  //             hidden: false,
  //             component: 'Medal/index',
  //             meta: {
  //               title: '等级勋章管理',
  //               icon: 'menu-medal',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Consume',
  //         path: '/Consume',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '消费记录管理',
  //           icon: 'menu-consume',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Consume',
  //             path: 'consume',
  //             hidden: false,
  //             component: 'Consume/index',
  //             meta: {
  //               title: '消费记录管理',
  //               icon: 'menu-consume',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'User',
  //         path: '/User',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '用户信息管理',
  //           icon: 'menu-user',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'User',
  //             path: 'user',
  //             hidden: false,
  //             component: 'User/index',
  //             meta: {
  //               title: '用户信息管理',
  //               icon: 'menu-user',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Dynamic',
  //         path: '/Dynamic',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '动态信息管理',
  //           icon: 'menu-dynamic',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Dynamic',
  //             path: 'dynamic',
  //             hidden: false,
  //             component: 'Dynamic/index',
  //             meta: {
  //               title: '动态信息管理',
  //               icon: 'menu-dynamic',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Comment',
  //         path: '/Comment',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '评论信息管理',
  //           icon: 'menu-comment',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Comment',
  //             path: 'comment',
  //             hidden: false,
  //             component: 'Comment/index',
  //             meta: {
  //               title: '评论信息管理',
  //               icon: 'menu-comment',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'Message',
  //         path: '/Message',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: true,
  //         meta: {
  //           title: '消息回复管理',
  //           icon: 'menu-message',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Letter',
  //             path: 'letter',
  //             hidden: false,
  //             component: 'Message/Letter/index',
  //             meta: {
  //               title: '私信消息列表',
  //               icon: 'menu-letter',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Reply',
  //             path: 'reply',
  //             hidden: false,
  //             component: 'Message/Reply/index',
  //             meta: {
  //               title: '自动回复设置',
  //               icon: 'menu-reply',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Group',
  //             path: 'group',
  //             hidden: false,
  //             component: 'Message/Group/index',
  //             meta: {
  //               title: '群发消息设置',
  //               icon: 'menu-group',
  //               noCache: false,
  //               link: null,
  //             },
  //           },    

  //         ]
  //       },
  //       {
  //         name: 'Answer',
  //         path: '/Answer',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: false,
  //         meta: {
  //           title: '问答信息管理',
  //           icon: 'menu-answer',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Answer',
  //             path: 'answer',
  //             hidden: false,
  //             component: 'Answer/index',
  //             meta: {
  //               title: '问答信息管理',
  //               icon: 'menu-answer',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //         ] 
  //       },
  //       {
  //         name: 'OtherInfo',
  //         path: '/OtherInfo',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: true,
  //         meta: {
  //           title: '其他信息管理',
  //           icon: 'menu-other',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'Area',
  //             path: 'area',
  //             hidden: false,
  //             component: 'OtherInfo/Area/index',
  //             meta: {
  //               title: '区域管理',
  //               icon: 'menu-area',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Notice',
  //             path: 'notice',
  //             hidden: false,
  //             component: 'OtherInfo/Notice/index',
  //             meta: {
  //               title: '系统公告通知',
  //               icon: 'menu-notify',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'AppInfo',
  //             path: 'appInfo',
  //             hidden: false,
  //             component: 'OtherInfo/appInfo/index',
  //             meta: {
  //               title: '用户端信息管理',
  //               icon: 'menu-banner',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
  //           {
  //             name: 'Label',
  //             path: 'label',
  //             hidden: false,
  //             component: 'OtherInfo/Label/index',
  //             meta: {
  //               title: '剧目标签管理',
  //               icon: 'menu-label',
  //               noCache: false,
  //               link: null,
  //             },
  //           },
            

  //         ]
  //       },

  //       {
  //         name: 'System',
  //         path: '/system',
  //         hidden: false,
  //         redirect: 'noRedirect',
  //         component: 'Layout',
  //         alwaysShow: true,
  //         meta: {
  //           title: '系统管理',
  //           icon: 'menu-setting',
  //           noCache: false,
  //           link: null,
  //         },
  //         children: [
  //           {
  //             name: 'user',
  //             path: 'user',
  //             hidden: false,
  //             component: 'system/user/index',
  //             meta: {
  //               title: '管理员设置',
  //               icon: 'table',
  //               noCache: false,
  //               link: null,
  //             },

  //           },
  //           {
  //             name: 'menu',
  //             path: 'menu',
  //             hidden: false,
  //             component: 'system/menu/index',
  //             meta: {
  //               title: '菜单信息管理',
  //               icon: 'table',
  //               noCache: false,
  //               link: null,
  //             },

  //           },
  //           {
  //             name: 'operlog',
  //             path: 'operlog',
  //             hidden: false,
  //             component: 'monitor/operlog/index',
  //             meta: {
  //               title: '日志信息管理',
  //               icon: 'table',
  //               noCache: false,
  //               link: null,
  //             },

  //           },
  //           {
  //             name: 'dict',
  //             path: 'dict',
  //             hidden: false,
  //             component: 'system/menu/index',
  //             meta: {
  //               title: '字典管理',
  //               icon: 'table',
  //               noCache: false,
  //               link: null,
  //             },

  //           },
  //         ]
  //       },
  //     ],
  //   }
  //   resolve(res)
  //   // reject(error)
  // })
}