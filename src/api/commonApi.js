import request from "@/utils/request";
// 查询列表
export function post(url, data) {
  return request({
    url: url,
    method: "post",
    data: data,
  });
}

export function get(url, params) {
  return request({
    url: url,
    method: "get",
    params,
  });
}

export function del(url, ids) {
  return request({
    url: url + "/" + ids,
    method: "delete",
  });
}

export function put(url, data, config = {}) {
  return request({
    url: url,
    method: "put",
    data: data,
    ...config,
  });
}
