import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus, { dayjs } from 'element-plus'
import locale from 'element-plus/lib/locale/lang/zh-cn' // 中文语言
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

import '@/assets/styles/index.scss' // global css

import TextClamp from 'vue3-text-clamp'

import JsonViewer from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins
import { download, downloadPost } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/ruoyi'
import { deepClone } from '@/utils/index'

// api接口
import api from '@/api/api.js'
// 通用方法
import * as fnc from '@/utils/basicHandle.js'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from '@/components/Editor'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'

// 封装的页面公共组件
import CommonTable from '@/components/CommonTable'
import BtnForCheck from '@/components/Btn/BtnForCheck'
import BtnForChangePw from '@/components/Btn/BtnForChangePw'
import BtnForLabel from '@/components/Btn/BtnForLabel'
// import StatusTip from '@/components/CommonTable/StatusTip'
import PlayInfo from '@/components/CommonTable/PlayInfo'
import SearchForm from '@/components/SearchForm'
import HandleRow from '@/components/HandleRow'
import DateRange from '@/components/DateRange'
import AddressSelect from '@/components/AddressSelect'
import CommonForm from '@/components/CommonForm'
import DefaultAvatar from '@/components/DefaultAvatar'
import PlayInfoMini from '@/components/CommonTable/PlayInfoMini'
import LevelMedal from '@/components/Medal'
import LineWrap from '@/components/LineWrap'
// test pic
import testImg from '@/assets/images/profile.jpg'
const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.downloadPost = downloadPost
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.deepClone = deepClone
app.config.globalProperties.$api = api

app.config.globalProperties.testImg = testImg

Object.keys(fnc).forEach((method) => (app.config.globalProperties[method] = fnc[method]))

// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)
app.component('Editor', Editor)
app.component('CommonTable', CommonTable)
app.component('SearchForm', SearchForm)
app.component('HandleRow', HandleRow)
app.component('DateRange', DateRange)
app.component('AddressSelect', AddressSelect)
app.component('CommonForm', CommonForm)
app.component('DefaultAvatar', DefaultAvatar)
app.component('BtnForCheck', BtnForCheck)
app.component('BtnForChangePw', BtnForChangePw)
app.component('BtnForLabel', BtnForLabel)
// app.component('StatusTip', StatusTip)
app.component('PlayInfo', PlayInfo)
app.component('PlayInfoMini', PlayInfoMini)
app.component('LevelMedal', LevelMedal)
app.component('LineWrap', LineWrap)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.use(TextClamp)
app.use(JsonViewer)
app.component('svg-icon', SvgIcon)

directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default',
})

app.mount('#app')
