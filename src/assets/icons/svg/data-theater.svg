<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份 8</title>
    <defs>
        <linearGradient x1="12.3881825%" y1="11.5747817%" x2="91.6941141%" y2="92.3740121%" id="linearGradient-1">
            <stop stop-color="#FFDB85" offset="0%"></stop>
            <stop stop-color="#FFB83C" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="MacBook-Air-13&quot;" transform="translate(-595.000000, -23.000000)">
            <g id="编组备份-8" transform="translate(595.000000, 23.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="64" height="64" rx="4"></rect>
                <g id="关注的机构" transform="translate(12.000000, 12.000000)">
                    <rect id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0" x="0" y="0" width="40" height="40"></rect>
                    <rect id="矩形" stroke="#FFFFFF" fill="#FFFFFF" x="4.5" y="14.5" width="32" height="1" rx="0.5"></rect>
                    <rect id="矩形备份" stroke="#FFFFFF" fill="#FFFFFF" x="4.5" y="34.5" width="32" height="1" rx="0.5"></rect>
                    <rect id="矩形备份" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(8.000000, 25.000000) rotate(-270.000000) translate(-8.000000, -25.000000) " x="-1.5" y="24.5" width="19" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-3" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(16.000000, 25.000000) rotate(-270.000000) translate(-16.000000, -25.000000) " x="6.5" y="24.5" width="19" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-4" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(24.000000, 25.000000) rotate(-270.000000) translate(-24.000000, -25.000000) " x="14.5" y="24.5" width="19" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-6" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(12.000000, 25.000000) rotate(-270.000000) translate(-12.000000, -25.000000) " x="8.5" y="24.5" width="7" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-7" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(20.000000, 25.000000) rotate(-270.000000) translate(-20.000000, -25.000000) " x="16.5" y="24.5" width="7" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-8" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(33.000000, 17.500000) rotate(-270.000000) translate(-33.000000, -17.500000) " x="31" y="17" width="4" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-9" stroke="#FFFFFF" fill="#FFFFFF" transform="translate(33.000000, 32.500000) rotate(-270.000000) translate(-33.000000, -32.500000) " x="31" y="32" width="4" height="1" rx="0.5"></rect>
                    <path d="M33.7,21.38 C34.48,21.38 35.22,21.68 35.76,22.24 C36.9,23.38 36.9,25.24 35.76,26.38 L33.24,28.9 C32.96,29.18 32.56,29.4 32.1,29.4 C31.76,29.4 31.4,29.28 31.02,28.98 C31,28.96 30.96,28.94 30.94,28.9 L28.42,26.38 C27.86,25.82 27.56,25.1 27.56,24.3 C27.56,23.52 27.86,22.78 28.42,22.24 C28.96,21.68 29.7,21.38 30.48,21.38 C31.06,21.38 31.62,21.54 32.08,21.86 C32.56,21.54 33.1,21.38 33.7,21.38 Z M30.5,23.68 C30.32,23.68 30.18,23.74 30.06,23.86 C29.94,23.98 29.88,24.14 29.88,24.32 C29.88,24.5 29.94,24.66 30.06,24.78 L32.12,26.84 L34.18,24.78 C34.42,24.54 34.42,24.12 34.18,23.88 C33.94,23.64 33.52,23.64 33.28,23.88 L32.98,24.18 C32.78,24.38 32.48,24.52 32.2,24.54 C31.88,24.56 31.56,24.46 31.32,24.22 L30.98,23.88 C30.84,23.74 30.68,23.68 30.5,23.68 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
                    <path d="M33.4404068,14.4237386 C31.947703,8.67126848 26.7203457,4.4237386 20.5009247,4.4237386 C14.3114511,4.4237386 9.10451036,8.63046204 7.58326476,14.3407467" id="路径" stroke="#FFFFFF" stroke-width="2"></path>
                </g>
            </g>
        </g>
    </g>
</svg>