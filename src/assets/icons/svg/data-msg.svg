<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份</title>
    <defs>
        <linearGradient x1="10.3583197%" y1="6.17073018%" x2="91.7258367%" y2="91.8220795%" id="linearGradient-1">
            <stop stop-color="#92E1AF" offset="0%"></stop>
            <stop stop-color="#59BF77" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none">
        <g id="MacBook-Air-13&quot;" transform="translate(-206.000000, -23.000000)">
            <g id="编组备份" transform="translate(206.000000, 23.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="64" height="64" rx="4"></rect>
                <g id="AK-MN_互动" transform="translate(12.000000, 12.000000)" fill-rule="nonzero">
                    <rect id="矩形" fill="#FFFFFF" opacity="0" x="0" y="0" width="40" height="40"></rect>
                    <path d="M3.94667524,16.5006955 L3.94667524,30.2498571 C3.94667524,31.364007 4.8216851,32.2681764 5.89619734,32.2681764 L8.12219013,32.2681764 C8.19220662,32.2804523 8.25892897,32.3070715 8.31816773,32.3463624 L8.32867797,34.0181641 C8.3298448,34.2731624 8.41469267,34.5207264 8.57018928,34.7228311 C8.94000247,35.2046681 9.48134413,35.4811709 10.0541845,35.4800454 C10.593187,35.4800454 11.1041835,35.2350132 11.4705039,34.806849 L13.6253283,32.7581845 C13.6964967,32.6905088 13.7991639,32.5458327 13.8504974,32.4618469 C13.9357543,32.362346 14.0520852,32.2945164 14.1806729,32.26933 L21.0029974,32.26933" id="路径" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M35.5095579,6.07554468 C35.0942319,5.15931543 34.2044492,4.51998437 33.1668821,4.51998437 L10.3530942,4.51998437 C9.31556982,4.51998437 8.42578711,5.15931543 8.01041846,6.0755874 C7.84730181,6.43257993 7.76194534,6.82017199 7.76000952,7.21266016 L7.76000952,25.5464309 C7.76000952,25.8093154 7.80820288,26.058229 7.87510962,26.2977859 C8.19084448,27.4162307 9.17556128,28.2391067 10.3515562,28.2391067 L22.113043,28.2391067 C22.283313,28.271754 22.437752,28.3604647 22.5517393,28.4910964 C22.6300998,28.6401815 22.7326004,28.7752657 22.855084,28.8908706 L25.7281858,31.6224258 C26.2166135,32.1948928 26.8979856,32.5199844 27.6150754,32.5199844 L27.6166135,32.5199844 C28.3804014,32.5199844 29.1021482,32.1528945 29.5952756,31.5104446 C29.8026046,31.2409714 29.9157352,30.9108858 29.917291,30.5708877 L29.9313047,28.3402358 C30.0096992,28.2884037 30.0982656,28.2539239 30.1910703,28.2391067 L33.1606016,28.2391067 C34.3397153,28.2391067 35.3275083,27.4162307 35.6432859,26.2977859 C35.7117307,26.058229 35.7599241,25.8093154 35.7599241,25.5464309 L35.7599241,7.21266016 C35.7579943,6.82015757 35.6726377,6.43255032 35.5095151,6.07554468 L35.5095579,6.07554468 Z" id="路径" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="round"></path>
                    <path d="M14.2926508,13.8817189 C13.5781196,13.8819608 12.9179946,14.2633823 12.5609386,14.8823054 C12.2038826,15.5012285 12.2041407,16.2636242 12.5616159,16.8823054 C12.919091,17.5009865 13.5794741,17.8819608 14.2940053,17.8817189 C15.008301,17.8814768 15.6682084,17.500181 16.0251467,16.8814618 C16.3820851,16.2627425 16.3818269,15.5005981 16.0244695,14.8821208 C15.6671121,14.2636435 15.0069465,13.8827949 14.2926508,13.8830368" id="路径" fill="#FFFFFF"></path>
                    <path d="M21.9571207,13.8817189 C20.8525512,13.8820828 19.9574158,14.7778083 19.9577796,15.8823778 C19.9581437,16.9869473 20.8538692,17.8820827 21.9584386,17.8817189 C23.0626442,17.8813548 23.9574846,16.9859244 23.9571208,15.8817189 C23.9567567,14.7775133 23.0613262,13.8826729 21.9571207,13.8830368" id="路径" fill="#FFFFFF"></path>
                    <path d="M29.9571207,13.8817189 C28.8525512,13.8820828 27.9574158,14.7778083 27.9577796,15.8823778 C27.9581437,16.9869473 28.8538692,17.8820827 29.9584386,17.8817189 C31.0626442,17.8813548 31.9574846,16.9859244 31.9571208,15.8817189 C31.9567567,14.7775133 31.0613262,13.8826729 29.9571207,13.8830368" id="路径备份" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </g>
</svg>