<template>
  <div class="imagesBox" :style="`width:${boxWidth}`">
    <el-image
      :src="src"
      fit="cover"
      :style="`width:${picWidth};height:${picHeight}`"
      class="pic"
      :preview-src-list="realSrcList"
      :initial-index="index"
      :previewTeleported="true"
      v-for="(src, index) in realSrc"
    >
      <template #error>
        <div class="image-slot">
          <el-icon><picture-filled /></el-icon>
        </div>
      </template>
    </el-image>
  </div>
</template>

<script setup>
import { isExternal } from "@/utils/validate";

const props = defineProps({
  src: {
    type: String,
    default: ""
  },
  width: {
    type: [Number, String],
    default: "50px"
  },
  height: {
    type: [Number, String],
    default: "50px"
  },
  picNumMax:{
    type:Number,
    default:1
  }
});
const realSrcList = computed(() => {
  if (!props.src) {
    return;
  }
  console.log(props.src)
  let real_src_list = props.src.split(",");
  let srcList = [];
  real_src_list.forEach(item => {
    if (isExternal(item)) {
      return srcList.push(item);
    }
    return srcList.push(import.meta.env.VITE_APP_BASE_API + item);
  });
  return srcList;
});


const realSrc = computed(() => {
  let src_arr = new Array(props.picNumMax).fill('')
  console.log(props.src)

  if (props.src) {
    let src_list = props.src.split(",")
    
    if(src_list.length){
      for(let index in src_arr){
        if(src_list[index]){

          if(isExternal(src_list[index])){
            src_arr[index] = src_list[index];
          }
          else{
            src_arr[index] = import.meta.env.VITE_APP_BASE_API + src_list[index];
          }
        }
        else{
          src_arr[index] = ''
        }
      }
    }
  }
  return src_arr.filter(i => i !== '')
});


// 计算图片容器和图片的宽高
const boxWidth = computed(() =>
  typeof props.width == "string" ? props.width : `${props.width}px`
);
const picWidth = computed(() => {
  const gutter = props.picNumMax > 3? 20 : ((props.picNumMax-1 )* 10)
  console.log('wirdh', (props.width - gutter) / 3)
  return props.picNumMax > 3 ? ((props.width - gutter) / 3) + 'px': ((props.width - gutter) / props.picNumMax) + 'px'
})
const boxHeight = computed(() =>
  typeof props.height == "string" ? props.height : `${props.height}px`
);
const picHeight = computed(() => {
  console.log(picWidth.value)
  return picWidth.value
})
</script>

<style lang="scss" scoped>
.imagesBox{
  display: flex;
  max-width: 330px;
  flex-wrap: wrap;
  width: fit-content;
  // margin-right: -10px;
  margin: 0 auto;
  margin-bottom: -10px;
}

.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  margin-right: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
  
  &:nth-child(3n){
    margin-right: 0;
  }
  &:first-child{
    margin-right: 10px;
  }
  &:last-child{
    margin-right: 0;
  }
  // box-shadow: 0 0 5px 1px #ccc;
  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      transform: scale(1.2);
    }
  }
  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
    background-color: #f8f8f9;
  }
}
</style>
