<template>
  <el-dropdown
    trigger="hover"
    style="margin-right: 12px; vertical-align: baseline"
    :disabled="props.disabled"
  >
    <el-button type="success" link :disabled="props.disabled">
      {{ props.disabled ? "已审核" : "审核" }}
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in options"
          :key="item.value"
          @click="dropClick(item)"
        >
          <el-link :type="item.type" :underline="false">{{
            item.label
          }}</el-link>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <el-dialog
    class="dialogWrap"
    :title="dialogControl.title"
    v-model="dialogControl.open"
    :width="dialogSize('small')"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    @closed="cancel"
    ref="dialogRef"
  >
    <el-form
      :model="dialogControl.form"
      ref="formRef"
      :inline="false"
      size="normal"
    >
      <el-form-item label="" :rules="rules" prop="reasonsRejection">
        <el-input
          v-model="dialogControl.form.reasonsRejection"
          type="textarea"
          :rows="5"
          placeholder="请输入驳回理由"
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getCurrentInstance } from "vue";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const emits = defineEmits();
const props = defineProps({
  rowId: {
    type: Number,
    default: 0,
  },
  req: {
    type: Function,
    default: () => {
      return new Promise((resolve, reject) => {
        resolve();
      });
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  hasPassEvent:{
    type:Boolean,
    default:false
  }
});

const options = ref([
  { label: "通过", value: "2", type: "success", event: pass },
  { label: "驳回", value: "1", type: "danger", event: reject },
]);

const dialogControl = ref({
  title: "驳回理由",
  open: false,
  form: {
    id: props.rowId,
    audit: "",
    reasonsRejection: "",
  },
});
function reject() {
  dialogControl.value.form.reasonsRejection = "";
  dialogControl.value.open = true;
}
function pass() {
  if(props.hasPassEvent){
    emits('pass')
    return
  }
  proxy.$modal.confirm('是否确认审核通过？').then(res => {
    dialogControl.value.form.audit = "2";
    dialogControl.value.form.reasonsRejection = "";
    dialogControl.value.form.id = props.rowId

    props.req(dialogControl.value.form).then((res) => {
      proxy.$modal.msgSuccess("已通过");
      emits("change");
      userStore.getPull()
    });
  }).catch(error => {})
}

function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if(valid){
      dialogControl.value.form.audit = "1";
      dialogControl.value.form.id = props.rowId
      props.req(dialogControl.value.form).then((res) => {
        proxy.$modal.msgSuccess("已驳回");
        dialogControl.value.open = false;
        emits("change");
      });
    }
  })
 
}
function dropClick(item) {
  item.event();
}


const rules = [
{required:true, message:'请输入驳回理由',trigger:''},
]
</script>

<style lang="scss"></style>
