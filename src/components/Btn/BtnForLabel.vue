<template>
  <el-button link type="primary" @click="clickEvent">修改标签</el-button>

  <el-dialog
    class="dialogWrap"
    :title="dialogControl.title"
    v-model="dialogControl.open"
    :width="500"
    append-to-body
    destroy-on-close
    draggable
    center
    :close-on-click-modal="false"
    @closed="cancel"
    ref="dialogRef">
    <el-form :model="dialogControl.form" ref="formRef" :inline="false" size="normal">
      <el-form-item label="剧目标签" size="normal">
        <Label v-model:value="dialogControl.form.repertoireLabelList" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getCurrentInstance, onMounted } from 'vue'
import Label from './label.vue'
const { proxy } = getCurrentInstance()
const emit = defineEmits()
const props = defineProps({
  rowId: {
    type: Number,
    default: 0,
  },
  labels: {
    type: String,
    default: undefined,
  },
})

const dialogControl = ref({
  title: '修改标签',
  open: false,
  form: {
    repertoireLabelList: [],
    repertoireId: props.rowId || undefined,
  },
})

function clickEvent() {
  // dialogControl.value.form.password = ''
  if (props.labels) {
    const labelArr = props.labels.split(',').map((i) => ({ name: i }))
    dialogControl.value.form.repertoireLabelList = labelArr
  }
  dialogControl.value.open = true
}
function submitForm() {
  // dialogControl
  //   console.log(dialogControl.value.form);
  dialogControl.value.form.repertoireId = props.rowId
  proxy.$api.repertoire.updateLabel(dialogControl.value.form).then((res) => {
    proxy.$modal.msgSuccess('修改标签成功')
    dialogControl.value.open = false
    emit('change')
    dialogControl.value.form.repertoireLabelList = []
  })
}

onMounted(() => {})
</script>

<style lang="scss"></style>
