<template>
   <div class="labelBox">
        <div class="labelRow">
            <el-tag
                v-for="(tag,index) in dynamicTags"
                :key="index"
                class="label"
                effect="plain"
                closable
                size="large"
                :disable-transitions="false"
                @close="closeLabel(tag)"
            >
                {{ tag.name }}
            </el-tag>
            <el-input
                v-if="inputVisible"
                ref="InputRef"
                v-model="inputValue"
                class="input"
                size="small"
                @keyup.enter="handleInputConfirm"
                @blur="handleInputConfirm"
            />
        </div>

        <div class="handleRow">
            <el-button size="small" @click="addLabel" class="btn">
                + 添加标签
            </el-button>
            <span>最多可以添加5个标签</span>
        </div>
   </div>
</template>

<script setup name="Label">
import { getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()

const emits = defineEmits()
const props = defineProps({
    value:{
        type:Array
    }
})


const inputVisible = ref(false)
const inputValue = ref('')
const dynamicTags = ref([
])

const addLabel = ()=> {
    if(dynamicTags.value.length === 5){
        return
    }
    inputVisible.value = true
    nextTick(() => {
        proxy.$refs['InputRef'].focus()
    })
}

const closeLabel = (tag) => {
    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
    emits('update:value', dynamicTags.value)
}
const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push({
        name:inputValue.value
    })
    emits('update:value', dynamicTags.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}


onMounted(() => {
    dynamicTags.value = props.value
})
</script>


<style lang="scss" scoped>
.labelBox{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .labelRow{
        display: flex;
        flex-wrap: wrap;
        .label{
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .input{
            width: 100px;
            margin-bottom: 10px;
            height: 32px;
        }
    }
    .handleRow{
        display: flex;
        align-items: flex-start;
        .btn{
            margin-right: 4px;
        }
        span{
            font-size: 12px;
            line-height: 24px;
        }
    }
}
    
</style>