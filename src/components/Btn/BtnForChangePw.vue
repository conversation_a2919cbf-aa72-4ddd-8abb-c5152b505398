<template>
    <el-button type="info " :link="!props.border" @click="clickEvent">
        重置密码
    </el-button>
  
    <el-dialog class="dialogWrap" :title="dialogControl.title" v-model="dialogControl.open" :width="400" append-to-body destroy-on-close draggable center :close-on-click-modal="false"
            @closed="cancel" ref="dialogRef">

            <el-form :model="dialogControl.form" ref="formRef" :inline="false" size="normal">
                <el-form-item label="" prop="password" :rules="rules">
                    <el-input v-model="dialogControl.form.password" placeholder="请输入新密码" ></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="cancel">取 消</el-button> -->
                </div>
            </template>
    </el-dialog>
</template>




<script setup>
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const props = defineProps({
    rowId: {
        type: Number,
        default: 0
    },
    border:{
        type:Boolean,
        default:false
    },
    req:{
        type:Function,
        default:undefined
    }
})


const dialogControl = ref({
    title: "重置密码",
    open: false,
    form: {
        password:''
    },
})
function clickEvent() {
    dialogControl.value.form.password = ''
    dialogControl.value.open = true
}
function submitForm() {
    // dialogControl\
    if(props.req){
        props.req({
            id:props.rowId,
            password:dialogControl.value.form.password
        }).then(res => {
            proxy.$modal.msgSuccess('密码重置成功')
            dialogControl.value.open = false
        })
    }
    else{
        proxy.$api.user.resetPwd({
            id:props.rowId,
            newPassword: dialogControl.value.form.password
        }).then(res => {
            proxy.$modal.msgSuccess('密码重置成功')
            dialogControl.value.open = false
        })
    }
}

const rules = [
    {required:true, message:'请输入新密码',trigger:''},
]
</script>

<style lang="scss"></style>