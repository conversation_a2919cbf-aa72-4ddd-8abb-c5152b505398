<template>
  <div class="search-row">
    <el-form :model="queryParams" class="queryForm" ref="queryRef" :inline="true" label-width="3em">
      <el-form-item label="" prop="merchantCategory" v-if="props.needPart">
        <el-radio-group v-model="queryParams.merchantCategory" size="normal" @change="handleQuery">
          <el-radio-button v-for="item in merchant_category_flag" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="" prop="tableType" v-if="props.needTab">
        <el-radio-group v-model="queryParams.tableType" size="normal">
          <el-radio-button :key="0" :label="0">分组</el-radio-button>
          <el-radio-button :key="1" :label="1">榜单</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <slot name="prefix"></slot>

      <template v-if="showSearch">
        <el-form-item label="" prop="keyword">
          <el-input @keyup.enter="handleQuery" clearable :placeholder="'请输入' + props.placeholder" v-model="queryParams.keyword" style="width: 200px" />
        </el-form-item>

        <el-button @click="handleQuery" icon="Search" plain type="">搜索</el-button>
        <el-button @click="handleReset" icon="Refresh" plain type="">重置</el-button>
      </template>
    </el-form>

    <el-row :gutter="10" class="handleBtns">
      <slot name="btns"></slot>
    </el-row>
  </div>
</template>

<script setup name="HandleRow">
import bus from '@/utils/bus'
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()
const { merchant_category_flag } = proxy.useDict('merchant_category_flag')
const props = defineProps({
  query: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 10,
      keyword: undefined,
    }),
  },
  placeholder: {
    type: String,
    default: '关键字',
  },
  needPart: {
    type: Boolean,
    default: false,
  },
  needTab: {
    type: Boolean,
    default: false,
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits()

const queryParams = ref({
  keyword: undefined,
})
function handleQuery() {
  console.log('querySearch', queryParams.value, props.query)
  // queryParams.value = props.query
  queryParams.value.pageNum = 1
  ;(queryParams.value.pageSize = 10), emits('update:query', queryParams.value)
  console.log('after', props.query)
  nextTick(() => {
    emits('search')
  })
}
function handleReset() {
  bus.emit('searchReset')
  
}

watch(
  () => props.query,
  (val) => {
    queryParams.value = props.query
  },
  { deep: true, immediate: true }
)
</script>

<!-- 需要兼顾slot中的dom样式，不可以使用scoped -->
<style lang="scss">
.queryForm {
  flex-grow: 1;
  margin-bottom: 8px;
  margin-left: 6px;

  .el-form-item {
    margin: 0;
    margin-right: 10px;

    .el-form-item__label {
      margin-right: 6px;
      padding: 0;
      color: #999999;
      font-weight: 400;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 32px;
    }

    .el-radio-group {
      flex-wrap: nowrap;
      width: fit-content;

      .el-radio {
        margin-right: 10px;

        .el-radio__input {
          display: none;
        }

        .el-radio__label {
          padding-left: 0;
        }

        &.is-bordered {
          padding: 0 9px;
        }
      }
    }
  }

  .el-date-editor {
    width: 300px;
  }

  :deep(.el-input) {
    width: 200px;
  }

  .keywordInput {
    width: 200px;
  }
}

.handleBtns {
  display: flex;
  flex-grow: 1;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
}
</style>
