
<template>
  <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" flexible :style="noGrow ? 'flex-grow: 0;' : ''" @expand-change="expand" :row-class-name="tableRowClassName">
    <el-table-column type="selection" width="55" align="center" v-if="props.hasSelect" fixed />
    <el-table-column label="序号" align="center" prop="index" width="55" fixed />

    <template v-for="(col, index) in props.tableCols" :key="index">
      <el-table-column :label="col.label" :prop="col.prop" header-align="center" :align="col.option?.align || 'center'" :show-overflow-tooltip="col.option?.showTip" :min-width="col.option?.minWidth || tableColWidth(col, tableData)" :fixed="col.option?.fixed || false" v-if="!props.needPart || needPartShowFnc(col)">
        <template #default="scope" v-if="col?.type">
          <div v-if="col.type === 'String'">
            {{ scope.row[col.prop] }}
          </div>

          <div v-if="col.type === 'Price'">
            <div v-if="scope.row[col.prop] != 0">
              <svg-icon class-name="yuan" icon-class="yuan" v-if="scope.row[col.prop]" />
              <span style="margin-left: 4px">{{ scope.row[col.prop] }}</span>
            </div>
            <div v-else>免费发放</div>
          </div>

          <div v-if="col.type === 'Time'">
            {{ parseTime(scope.row[col.prop]) }}
          </div>

          <!-- <el-image :src="scope.row[col.prop]" fit="fill" ></el-image> -->
          <ImagePreview v-if="col.type === 'Image'" :src="col.option?.changeFnc ? col.option.changeFnc(scope.row[col.prop]) : scope.row[col.prop]" :width="col.option.width || 100" :height="100" :picNumMax="col.option.picNumMax || 1" />

          <div v-if="col.type === 'DoubleImage'">
            <ImagePreview :src="scope.row[col.option.vals[0]] + ',' + scope.row[col.option.vals[1]]" :width="200" :height="100" :picNumMax="2" />
          </div>

          <dict-tag v-if="col.type === 'Dict'" :options="col.option.pull" :value="scope.row[col.prop]" />
          <!-- <div v-if="col.type === 'Dict'">
                        {{ col.option.pull }}
                        {{ scope.row[col.prop] }}
                    </div> -->

          <Audit v-if="col.type === 'Audit'" :options="col.option.pull" :value="scope.row[col.prop]" :text-tip="scope.row[col.option.tip]" />

          <slot v-if="col.type === 'Slot'" :name="col.prop" :data="{ scope, col }"></slot>

          <DefaultAvatar v-if="col.type === 'Avatar'" :url="scope.row[col.prop]" />

          <el-switch v-if="col.type === 'Switch'" v-model="scope.row[col.prop]" :active-value="1" :inactive-value="0" :disabled="scope.row.defaultFlag === 1" @change="handleStatusChange($event, scope.row, col)"></el-switch>

          <PlayInfo v-if="col.type === 'PlayInfo'" :form="scope.row" />

          <div v-if="col.type === 'Select'">
            {{ getValue(scope.row[col.prop], col.option.pull) }}
          </div>

          <div v-if="col.type === 'issueTime'">
            {{ scope.row['startTime'] + ' - ' + scope.row['endTime'] }}
          </div>

          <div v-if="col.type === 'receivedNumber'">
            <receivedNumber :row="scope.row" :val="scope.row[col.prop]" :type="col.option.type" :upgrade="col.option.upgrade" />
          </div>

          <div v-if="col.type === 'hasSold'" style="font-size: 16px">
            <div v-if="scope.row.audit !== 2">-</div>
            <div v-else>
              <div v-if="scope.row.status === 0" style="color: var(--el-color-info)">已下架</div>
              <div v-else>
                <div v-if="scope.row.soldOut" style="color: var(--el-color-warning)">已售罄</div>
                <div v-else style="color: var(--el-color-success)">已上架</div>
              </div>
            </div>
          </div>

          <div v-if="col.type === 'QR'">
            <qrcode :src="scope.row[col.prop]" />
          </div>
        </template>
      </el-table-column>
    </template>

    <slot></slot>
  </el-table>

  <slot name="comment"></slot>

  <pagination v-show="total > 0" :total="total" v-model:page="props.queryParams.pageNum" v-model:limit="props.queryParams.pageSize" @pagination="getList" />
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import PlayInfo from './PlayInfo.vue'
import Audit from './audit.vue'
import qrcode from './qrcode.vue'
import receivedNumber from './receivedNumber.vue'
const { proxy } = getCurrentInstance()
const emits = defineEmits()
const props = defineProps({
  listReq: {
    type: Function,
    default: () => { },
  },
  statusReq: {
    type: Function,
    default: () => { },
  },
  queryParams: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 10,
    }),
  },
  tableCols: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
  },
  hasSelect: {
    type: Boolean,
    default: true,
  },
  formatSubForm: {
    type: Function,
    default: (data) => {
      return data
    },
  },
  formatGetForm: {
    type: Function,
    default: (data) => {
      return data
    },
  },
  needPart: {
    type: Boolean,
    default: false,
  },
  noGrow: {
    type: Boolean,
    default: false,
  },
})
/* 数据查询 */
const tableData = ref([])
const loading = ref(true)
const total = ref(0)

function getList() {
  loading.value = true
  props
    .listReq(props.queryParams)
    .then((response) => {
      tableData.value = response.data.rows
      tableData.value.forEach((i) => (i.index = (props.queryParams.pageNum - 1) * props.queryParams.pageSize + tableData.value.indexOf(i) + 1))
      total.value = response.data.total
      loading.value = false
      // proxy.$modal.msgSuccess('数据更新')
      emits('forExpand', response.data.rows)
    })
    .catch((err) => {
      loading.value = false
    })
}

/** 多选框选中数据 */
const ids = ref([])
const indexs = ref([])
const single = ref(true)
const multiple = ref(true)
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
  indexs.value = selection.map((item) => item.index).sort((a, b) => a - b)
  emits('update:multiple', multiple.value)
}

/* 对表格的操作函数 */
function handleOpenDialog(dialogControl, title) {
  dialogControl.open = true
  dialogControl.title = title
}
function handleDelete(remove, row) {
  console.log('🚀 ~ handleDelete ~ remove, row🚀', remove, row)
  const deleteIds = row?.id || ids.value
  const deleteIndexs = row?.index || indexs.value
  proxy.$modal
    .confirm('是否确认删除所选的数据项？')
    .then(function () {
      return remove(deleteIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
      emits('hasDelete')
    })
    .catch(() => { })
}
function handleGetDetail(detail, row, dialogControl, title, fn) {
  // console.log(detail, row, dialogControl, title)
  const params = {
    id: row.id,
  }
  detail(params).then(async (response) => {
    // console.log(props.formatGetForm,response.data)
    if (fn) await fn(response)
    dialogControl.form = proxy.deepClone(props.formatGetForm(response.data))
    dialogControl.open = true
    dialogControl.title = title
    emits('hasDetail')
  })
}
function handleSubmit(add, update, dialogControl) {
  const postForm = props.formatSubForm(dialogControl.form)
  if (dialogControl.form.id != undefined) {
    update(postForm).then((response) => {
      proxy.$modal.msgSuccess('修改成功')
      dialogControl.open = false
      getList()
      emits('hasUpdate')
    })
  } else {
    add(postForm).then((response) => {
      proxy.$modal.msgSuccess('新增成功')
      dialogControl.open = false
      getList()
      emits('hasAdd')
    })
  }
}

function handleStatusChange(data, row, col) {
  // console.log('chufa', data)
  let text = row[col.prop] == '1' ? col.option.actionOnName || '启用' : col.option.actionOffName || '停用'
  let tip = row[col.option.tipName] ? `"${row[col.option.tipName]}"` : '该数据项'
  proxy.$modal
    .confirm('确认要"' + text + '"' + tip + '吗?')
    .then(function () {
      // 传递更多参数：id, 字段名, 新值, 行数据
      return props.statusReq(row.id, col.prop, data, row)
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + '成功')
    })
    .catch(function () {
      // 恢复原值时应该使用对应的字段名
      row[col.prop] = row[col.prop] === 0 ? 1 : 0
    })
}

/* 表格宽度 */
function tableColWidth(col, tableData) {
  return proxy.flexWidth(col.prop, tableData, col.label)
}

onMounted(() => {
  getList()
})

defineExpose({
  getList,
  handleOpenDialog,
  handleDelete,
  handleGetDetail,
  handleSubmit,
  ids,
  single,
  multiple,
  tableData,
})

/* 为了控制某些特殊行的显隐 */
function needPartShowFnc(col) {
  if (col.prop === 'repertoireName') {
    return props.queryParams.merchantCategory === '1'
  } else if (col.prop === 'theaterName') {
    return props.queryParams.merchantCategory === '2'
  } else {
    return true
  }
}

function expand(row, expandedRows) {
  emits('expandChange', row, expandedRows)
}

function getValue(val, options) {
  if (Array.isArray(options) && options.length > 0) {
    const afterFliter = options.filter((i) => i.value == val)
    if (afterFliter.length > 0) {
      return afterFliter[0].label
    }
    return val
  }
  return val
}

function tableRowClassName({ row, rowIndex }) {
  if (row.status === 0) {
    return 'disabled-row'
  }
  return ''
}
</script>

<style lang="scss">
.el-table .disabled-row {
  color: #999999;
  .intro {
    .info {
      .name {
        color: #999999;
      }
    }
  }
}
</style>
