<template>
  <div class="intro">
    <el-image class="cover" preview-teleported :preview-src-list="[addBaseUrl(form.coverPicture)]" :src="addBaseUrl(form.coverPicture)" fit="cover"></el-image>
    <div class="info">
      <div class="name">
        {{ form.name }}
        <div class="shortName" v-if="form.shortName">{{ form.shortName }}</div>
      </div>
      <div class="text1">
        <div class="tags" v-if="form.repertoireLabel">
          <div class="tag" v-for="(tag, index) in form.tags" :key="index">
            {{ tag }}
          </div>
        </div>

        <!-- <div class="score">
                    <el-rate
                        v-model="form.goodRatingRate"
                        disabled
                        show-score
                        text-color="#ff9900"
                        allow-half
                        score-template="{value}"
                    >
                    </el-rate>
                </div> -->
      </div>
      <div class="text2">
        {{ parseTime(form.createTime) }}
        <span class="medalName">
          {{ form.rankMedalName }}
        </span>
        <span class="medalName">
          {{ form.merchantName }}
        </span>
      </div>
      <div class="text3" :title="htmlToStr(form.introduction)">
        {{ htmlToStr(form.introduction) }}
      </div>
      <MarkList style="max-width: 220px" :count="markCount" />
    </div>
  </div>
</template>

<script setup name="PlayInfo">
import { getCurrentInstance } from 'vue'
import MarkList from '../../views/Theater/detail/markList.vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
  form: {
    type: Object,
    default: {
      name: '',
      coverPicture: '',
      createTime: '',
      rating: 0,
      repertoireLabel: '',
      introduction: '',
    },
  },
})
const form = computed(() => {
  const obj = {}
  if (props.form.repertoireLabel) {
    obj.tags = props.form.repertoireLabel.split(',')
  } else {
    obj.tags = []
  }
  console.log('changePlayInfo')
  return { ...props.form, tags: obj.tags }
})
const markCount = computed(() => {
  return [props.form.focusNumber, props.form.commentCount, props.form.interactionCount]
})
onMounted(() => {
  // console.log(props.form, '====')
  // form.value = props.form
})
</script>

<style lang="scss" scoped>
.intro {
  display: flex;
  align-items: flex-start;
  // flex-grow: 1;
  width: 100%;
  // width:0
  .cover {
    width: 183px;
    height: 133px;
    flex-shrink: 0;
  }
  .info {
    padding: 0 22px;
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
    overflow: hidden;
    flex-grow: 1;
    .name {
      color: #333333;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      .shortName {
        font-size: 14px;
        line-height: 18px;
        background-color: #eeeeee;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
        color: #999999;
        display: inline-block;
      }
    }
    .text1 {
      display: flex;
      align-items: center;
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
      flex-wrap: wrap;
      min-height: 24px;
      // margin-bottom: 8px;
      .tags {
        display: flex;
        margin-right: -4px;
        margin-bottom: -4px;
        // overflow: hidden;
        display: flex;
        flex-wrap: wrap;
      }
      .tag {
        // width: 39px;
        padding: 0 6px;
        height: 24px;
        line-height: 24px;
        border-radius: 5px 5px 5px 5px;
        background-color: rgba(253, 187, 44, 0.8);
        text-align: center;
        margin-right: 4px;
        margin-bottom: 4px;
        color: rgba(255, 255, 255, 1);
        flex-shrink: 0;
      }

      .score {
        :deep(.el-rate__text) {
          color: rgba(253, 187, 44, 0.8);
        }
        flex-shrink: 0;
      }
    }
    .text2 {
      // margin-bottom: 8px;
      line-height: 27px;
      .medalName {
        margin-left: 10px;
      }
    }
    .text3 {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // margin-bottom: 8px;
      line-height: 27px;
    }
  }
  &:deep(.mark) {
    justify-content: flex-start;
  }
}
</style>
