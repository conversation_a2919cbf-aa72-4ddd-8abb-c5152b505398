<template>
    <div class="auditBox">
        <dict-tag :options="props.options" :value="props.value"/>
        <el-tooltip
            effect="dark"
            :content="props.textTip || '未填写原因'"
            placement="top"
            v-if="dangerValue == props.value"
        >
        <el-icon class="danger-icon"><Warning /></el-icon>
        </el-tooltip>
    </div>
    
</template>

<script setup name="Audit">
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance()

const props = defineProps({
    options:{
        type:Array,
        default:null
    },
    value:{
        type:[String, Number]
    },
    textTip:{
        type:String,
        default:''
    }
})
const dangerValue = computed(()=> {
    var value = ''
    props.options.forEach(i => {
        if(i.elTagType === 'danger') value = i.value
    })
    return value
})
</script>

<style lang="scss" scoped>

.auditBox{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:deep(.el-tag){
        background-color: transparent;
        border: none;
        padding: 0;
        line-height: 16px;
        font-size: 16px;
    }
    .danger-icon{
        color:  var(--el-color-error);
        font-size: 20px;
        margin-left: 4px;
    }
}

</style>