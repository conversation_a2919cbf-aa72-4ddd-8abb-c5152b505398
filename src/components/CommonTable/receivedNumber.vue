<template>
    <div>
        <el-button text type="warning" size="default" :disabled="props.val === 0" @click="getNumList">{{ props.val }}</el-button>
        
    </div>


    <!-- 添加或修改公告对话框 -->
    <el-dialog
      class="dialogWrap"
      :title="dialogControl.title"
      v-model="dialogControl.open"
      :width="dialogSize('default')"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      @closed="cancel"
      ref="dialogRef"
    >

        <el-table
            :data="dialogControl.tableData"
            style="width: 100%"
            >
            <el-table-column
              label="序号"
              width="60"
              type="index"
            ></el-table-column>

            <el-table-column
              label="用户信息"
              align="center"
              prop="userInfo"
            >
                <template #default='scope'>
                    <UserInfo
                        :userName="scope.row.userName"
                        :phone="scope.row.phone"
                        :color="scope.row.rankMedalColor"
                        :rankMedalName="scope.row.rankMedalName"
                        :rankMedalLevel="scope.row.rankMedalLevel"
                    />
                </template>
            </el-table-column>
            <el-table-column
              label="领取时间"
              align="center"
              prop="createTime"
              minWidth="200"
            ></el-table-column>
        </el-table>

        <pagination v-show="dialogControl.total > 0" :total="dialogControl.total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        
    </el-dialog>

</template>


<script setup name="receivedNumber">
import { getCurrentInstance } from 'vue';
import UserInfo from "../../views/Consume/detail/userInfo.vue";
const { proxy } = getCurrentInstance()

const { badge_type_flag } = proxy.useDict("badge_type_flag");

const props = defineProps({
    row:{
        type:Object,
        default:undefined
    },
    type:{
        type:[Number, String],
        default:undefined
    },
    upgrade:{
        type:Boolean,
        default:false
    },
    val:{
        type:Number,
        default:2
    }
})

/* 操作和表单配置 */
const dialogControl = ref({
  open: false,
  title: "",
  tableData: [],
  readonly: false,
  total:0
});

const queryParams = ref({
    pageNum:1,
    pageSize:10,
    relationId:undefined,
    badgeType:undefined,
    upgradeStatus:0
})

function getList(){
    if(props.row.id){
        queryParams.value.relationId = props.row.id
        queryParams.value.badgeType = props.type
        queryParams.value.upgradeStatus = props.upgrade?1:undefined
        proxy.$api.userReceivingRecords.findCollectionRecipient(queryParams.value).then(res => {
            dialogControl.value.tableData = res.data.rows
            dialogControl.value.total = res.data.total
        })
    }
}

function getNumList(){
    getList()
    dialogControl.value.open = true
    const type = badge_type_flag.value.filter(i => {
        return i.value == props.type
    })
    console.log(type,badge_type_flag.value, props.type)

    dialogControl.value.title = type[0].label + '领取人数  ' + props.val
}

onMounted(() => {
    // getList()
})
// if(props.row)

</script>