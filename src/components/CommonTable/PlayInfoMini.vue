<template>
    <div class="playInfoMiniBox">
      <div class="playImage">
        <el-image
          style="width: 100%; height: 100%"
          :src="imgUrl + props.cover"
          fit="fill"
          :lazy="true"
        ></el-image>
      </div>
  
      <div class="playInfo">
        <div class="name">
          {{ props.name }}
        </div>
        <div class="info">
          <div class="text" v-if="props.theaterName">
            <span>演出场地</span>
            <span>{{ props.theaterName }}</span>
          </div>
          <div class="text" v-if="props.session">
            <span>演出场次</span>
            <span>{{ props.session }}</span>
          </div>
          <div class="text" v-if="props.subTime">
            <span>关注时间</span>
            <span>{{ props.subTime }}</span>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup name="playInfoMini">
  import { getCurrentInstance } from "vue";
  
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    cover: {
      type: String,
      default: undefined,
    },
    name: {
      type: String,
      default: undefined,
    },
    subTime: {
      type: String,
      default: undefined,
    },
    theaterName: {
      type: String,
      default: undefined,
    },
    session: {
      type: String,
      default: undefined,
    },
  });
  
  const imgUrl = import.meta.env.VITE_IMAGE;
  </script>
  
  <style lang="scss" scoped>
  .playInfoMiniBox {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 8px 18px;
    .playImage {
      flex-shrink: 0;
      width: 80px;
      height: 60px;
      border-radius: 10px;
      overflow: hidden;
      margin-right: 10px;
    }
    .playInfo {
      flex-grow: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .name {
        font-weight: 600;
        font-size: 18px;
        text-align: left;
        line-height: 30px;
      }
      .info {
        display: flex;
        flex-wrap: wrap;
        .text {
          line-height: 30px;
          margin-right: 30px;
          span {
            margin-right: 8px;
          }
        }
      }
    }
  }
  </style>
  