<template>
     <el-popover
        placement="top"
        :width="160"
        trigger="hover"
        popper-style="width:160px;height:160px;padding:10px"
    >
        <template #reference>
            <el-button class="m-2" link @click="dowmImg" v-if="props.src">点击下载</el-button>
        </template>
        <template #default>
            <img :src="baseUrl + props.src" alt="" style="width: 140px; height: 140px;">
        </template>
    </el-popover>
    
</template>
<script setup name="qrcode">
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance()
const props = defineProps({
    src:{
        type:String,
        default:''
    }
})
const baseUrl = ref(import.meta.env.VITE_IMAGE)
function dowmImg(){
    proxy.$download.resource(props.src);
    // console.log('下载图片')
}
</script>

<style>
</style>