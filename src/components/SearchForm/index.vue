<template>
  <el-dialog :title="title" append-to-body class="dailogWrap" draggable v-model="props.open" width="800px" :close-on-click-modal="false" @close="dialogClose">
    <!-- 筛选 -->
    <el-form :model="queryParams" class="queryDialogFrom" ref="queryRef" label-width="6em">
      <!-- <el-form-item label="关键字" prop="keyword">
        <el-input @keyup.enter="handleQuery" clearable placeholder="请输入关键字" v-model="queryParams.keyword" />
      </el-form-item> -->
      <el-form-item :label="item.label" :prop="item.prop" v-for="(item, index) in props.options" :key="index" v-show="!item.option.hidden">
        <!-- 多选组 -->
        <div v-if="item.type === 'tags' || item.type === 'age'" class="checkGroup">
          <el-checkbox border v-model="queryParams[item.prop].all" @change="allCheck($event, item.prop, item.option.pull)" class="allCheck">全部</el-checkbox>

          <el-checkbox-group v-model="queryParams[item.prop].vals" @change="checkChange($event, item.prop, item.option.pull)">
            <el-checkbox :key="chItem.value" :label="chItem.value" border v-for="chItem in item.option.pull">{{ chItem.label }}</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 输入框 -->
        <el-input v-if="item.type === 'input'" v-model="queryParams[item.prop].vals" :placeholder="`请输入${item.label}`"></el-input>

        <!-- <div class="moreBtn" @click="moreFilter">
          <el-icon :size="14" color="#dcdfe6">
            <ArrowUp />
          </el-icon>
        </div>
        <div class="moreBtn">
          <el-icon :size="14" color="#39A2E1">
            <ArrowDown />
          </el-icon>
        </div> -->

        <!-- 下拉多选 -->
        <el-select-v2
          class="multipleSelect"
          :height="340"
          :options="item?.option?.pull"
          :placeholder="`请选择${item.label}`"
          clearable
          collapseTags
          collapseTagsTooltip
          filterable
          multiple
          v-if="item.type === 'multipleSelect'"
          v-model="queryParams[item.prop].vals">
        </el-select-v2>

        <!-- @change="checkChange($event, item.prop, item.option.pull)" -->
        <!-- <el-checkbox
          class="allCheck allCheckPatch"
          @change="allCheck($event, item.prop, item.option.pull)"
          border
          v-if="item.type === 'multipleSelect'"
          v-model="queryParams[item.prop].all"
          >全选{{ item.label }}</el-checkbox
        > -->

        <!-- 下拉单选 -->
        <el-select
          v-if="item.type === 'inputSelect'"
          :placeholder="`请输入${item.label}`"
          v-model="queryParams[item.prop]"
          filterable
          remote
          reserve-keyword
          :remote-method="(query) => remoteMethod(query, item.options)"
          :loading="item.options.loading">
          <el-option :key="listItem.id" :label="listItem.name" :value="listItem.id" v-for="listItem in item.options.list" />
        </el-select>

        <!-- 下拉单选 -->
        <el-select clearable v-if="item.type === 'select'" :placeholder="`请选择${item.label}`" v-model="queryParams[item.prop].vals">
          <el-option :key="listItem.value" :label="listItem.label" :value="listItem.value" v-for="listItem in item.option.list" />
        </el-select>

        <!-- 时间选择 -->
        <el-date-picker
          v-model="queryParams[item.prop].vals"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          v-if="item.type === 'daterange'"
          clearable></el-date-picker>
        <!-- <DateRange v-model:value="queryParams[item.prop].vals" value-format="YYYY-MM-DD"  v-if="queryParams[item.prop]?.vals && item.type === 'daterange'"/> -->

        <!-- 地址选择 -->
        <AddressSelect :options="areaTree" v-model:value="queryParams[item.prop].vals" v-if="item.type === 'address'" />

        <!-- <PartPostSelect v-model:value="queryParams[item.prop].vals" :options="[{value:'departmentId', label:'部门'}, {value:'postId', label:'岗位'}]" v-if="item.type === 'partPost'"/> -->
      </el-form-item>

      <div class="btns">
        <el-button @click="handleQuery" icon="Search" type="primary">搜索</el-button>
        <el-button @click="reset" icon="Refresh">重置</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup>
// import { pullAreaTree, pullPart } from '@/api/statistic/pull'
import bus from '@/utils/bus'
import { getCurrentInstance, nextTick } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  query: {
    type: Object,
    required: true,
  },
  open: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => {},
  },
  needPart: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits()
const title = ref('搜索条件')
const queryParams = ref({})
const areaTree = ref({})
const partOptions = ref([])
const isReset = ref(false)

watch(
  () => props.options,
  (val) => {
    // console.log('optionsChange')
    resetQuery()
    isReset.value = false
  },
  { immediate: true, deep: true }
)

watch(
  () => queryParams.value,
  (old, val) => {
    // console.log(val)
    const param = formatQuery(queryParams.value)
    // console.log('para',param)
    if (isReset.value) param.keyword = ''
    emits('update:query', param)
    if (isReset.value) {
      // console.log('queryChange')
      nextTick(() => {
        emits('search')
      })
    }
    isReset.value = false
  },
  { deep: true }
)

onMounted(() => {
  // pullAreaTree().then(response => {
  //     areaTree.value = response.data
  // })
  bus.on('searchReset', reset)
})

if (props.needPart) {
  watch(
    () => props.query.merchantCategory,
    (old, val) => {
      // console.log('changePart')
      if (props.query.merchantCategory === '1') {
        props.options.forEach((obj) => {
          if (obj.prop === 'theaterId') {
            obj.option.hidden = true
          }
          if (obj.prop === 'repertoireId') {
            obj.option.hidden = false
          }
        })
      } else if (props.query.merchantCategory === '2') {
        // queryObj['repertoireId'].disabled = true
        props.options.forEach((obj) => {
          if (obj.prop === 'repertoireId') {
            obj.option.hidden = true
          }
          if (obj.prop === 'theaterId') {
            obj.option.hidden = false
          }
        })
      }
    },
    { deep: true, immediate: true }
  )
}

function handleQuery() {
  // let ar = formatQuery(queryParams.value)
  // emits('update:query', ar)
  queryParams.value.pageNum = {
    vals: 1,
  }
  emits('search')
  emits('update:open', false)
}
function resetQuery() {
  console.log('search reset')
  const obj = [...props.options]
  // console.log('obj',obj)
  const queryObj = {}
  // console.log('obj',obj)
  for (let key in obj) {
    queryObj[obj[key].prop] = {
      vals: [],
      all: false,
      type: obj[key].type,
      // disabled:obj[key].option.hidden
    }
  }

  isReset.value = true
  queryParams.value = queryObj

  // emits('reset')
}

function formatQuery(query) {
  console.log('🚀 ~ formatQuery ~ query🚀', query)
  const form = {}
  for (let key in query) {
    if (query[key].type == 'tags') {
      form[key] = query[key].vals.join(',')
    } else if (query[key].type == 'daterange') {
      // console.log('11',form[key])
      form[key] = {
        beginTime: query[key].vals && query[key].vals[0],
        endTime: query[key].vals && query[key].vals[1],
      }
    } else if (query[key].type == 'address') {
      form[key] = Array.isArray(query[key].vals) ? query[key].vals.join(',') : query[key].vals
    }

    // else if(query[key].type == 'partPost'){
    //     form['departmentId'] = query[key].vals[0];
    //     form['postId'] = query[key].vals[1]
    // }
    else {
      form[key] = Array.isArray(query[key].vals) ? query[key].vals.join(',') : query[key].vals
    }
  }
  const postForm = { ...props.query, ...form }
  // console.log('wqw',postForm)
  return postForm
}
function reset() {
  isReset.value = true
  resetQuery()

  // emits('search')
}
function dialogClose() {
  emits('update:open', false)
}

function checkChange(data, prop, options) {
  const checkedLength = data.length
  queryParams.value[prop].all = checkedLength === options.length
}
function allCheck(data, prop, options) {
  if (data) {
    queryParams.value[prop].vals = options.map((i) => i.value)
  } else {
    queryParams.value[prop].vals = []
  }
}

function remoteMethod(query, options) {
  if (query !== '') {
    // console.log(query)
    const optionsReq = options.request
    options.loading = true
    optionsReq({ organizeId: options.organizeId, name: query }).then((res) => {
      options.loading = false
      options.list = res.data
    })
  } else {
    options.list = []
  }
}
</script>

<style lang="scss" scoped>
.queryDialogFrom {
  &:deep(.el-form-item) {
    // align-items: center;
    margin-bottom: 7px;
  }

  &:deep(.el-form-item__label) {
    margin-right: 12px;
    padding: 0;
    color: #999999;
    font-weight: 400;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 32px;
  }

  &:deep(.el-date-editor.el-input__wrapper) {
    flex-grow: 0;
    max-width: 250px;
  }

  &:deep(.el-form-item__content) {
    min-width: 260px;
    padding-right: 20px;

    // max-height: 74px;
    overflow: hidden;

    .el-input {
      max-width: 250px;

      .el-input__wrapper {
        // box-shadow: 0 0 0 1px #d9d9d9 inset;
        .el-input__inner {
          // color: #222222 !important;
          font-weight: 400 !important;
          font-size: 14px !important;
          font-family: PingFangSC-Regular, PingFang SC !important;

          &::placeholder {
            font-weight: 400 !important;
            font-size: 14px !important;
            font-family: PingFangSC-Regular, PingFang SC !important;
            // color: rgba(0, 0, 0, 0.25) !important;
          }
        }
      }
    }

    .el-select {
      width: 250px;
    }

    .el-cascader {
      width: 100%;

      &.is-disabled {
        .el-input__wrapper {
          background: #ffffff;
        }
      }

      .el-input__inner {
        color: #222222;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;

        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .el-radio-group {
      flex-wrap: nowrap;
      width: fit-content;

      .el-radio {
        margin-right: 10px;

        .el-radio__input {
          display: none;
        }

        .el-radio__label {
          padding-left: 0;
        }

        &.is-bordered {
          padding: 0 9px;
        }
      }
    }

    .el-checkbox-group {
      width: fit-content;
      margin: -5px 0;

      .el-checkbox {
        padding: 5px 0;
        margin: 5px 10px 5px 0;

        // margin-top: 5px;
        // margin-bottom: 5px;
        .el-checkbox__input {
          display: none;
        }

        .el-checkbox__label {
          padding-left: 0;
        }

        &.is-bordered {
          padding: 0 9px;
        }
      }
    }

    .moreBtn {
      display: flex;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      // max-height: 70px;
      height: 100%;
      align-items: center;
      width: fit-content;
      position: absolute;
      right: 4px;
      padding: 0 4px;
      cursor: pointer;

      &.isActive {
        border: 1px solid #39a2e1;
      }
    }

    .addressSelectBox {
      margin-bottom: 5px;
    }
    .el-date-editor {
      margin-bottom: 5px;
    }
  }
  .isActive {
    .el-form-item__content {
      max-height: fit-content;
    }

    .moreBtn {
      border: 1px solid #39a0de;
    }
  }

  .rowItem {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }

  .btns {
    // margin-right: 0;
    margin-top: 40px;
    width: 100%;
    display: flex;
    justify-content: center;

    .el-button {
      width: 200px;
    }

    .el-form-item__content {
      width: fit-content;
    }
  }
  .allCheck {
    // margin: 5px 10px 5px 0;
    margin-right: 10px;
    display: inline-flex;
    padding: 0 9px;

    &:deep(.el-checkbox__input) {
      display: none;
    }

    &:deep(.el-checkbox__label) {
      padding-left: 0;
    }

    &.allCheckPatch {
      align-self: start;
      margin-right: 0;
      margin-left: 20px;
    }
  }
  .checkGroup {
    // display: flex;
    .el-checkbox-group {
      display: inline;
      .el-checkbox {
        margin-top: 0;
        // margin-bottom: 0;
      }
    }
  }

  .multipleSelect {
    width: 250px;
  }
}
</style>
