<template>
    <div class="medalBox" :style="domStyle1" v-if="props.level">
        <div class="name">
            {{ props.name || 'no rankMedalName' }}
        </div>
        <div class="level" :style="domStyle2">
            {{ props.level || 'LV1' }}
        </div>
    </div>
</template>

<script setup name="Medal">

const props = defineProps({
    name:{
        type:String,
        default:undefined
    },
    level:{
        type:String,
        default:undefined
    },
    color:{
        type:String,
        default:undefined
    }
})


const domStyle1 = computed(()=> {
    return {
        backgroundColor:props.color || '#009944'
    }
})

const domStyle2 = computed(() => {
    return {
        color:props.color || '#009944',
        border: `2px solid ${props.color || '#009944'}`
    }
})
</script>

<style lang="scss" scoped>
.medalBox{
    display: flex;
    line-height: 16px;
    font-size: 12px;
    // background-color: #009944;
    border-radius: 4px;
    justify-content: space-between;
    width: fit-content;
    // margin: 0 auto;
    .name{
        padding: 0 4px;
        color: #fff;
        display: flex;
        align-items: center;
    }
    .level{
        padding: 0 4px;
        background-color: #fff;
        flex-shrink: 0;
        // color: #009944;
        // border: 2px solid #009944;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }
}

</style>