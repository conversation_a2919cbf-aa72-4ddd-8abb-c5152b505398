<template>
    <div class="addressSelectBox">
        <span class="label">省份</span>
        <el-select clearable placeholder="请选择省" v-model="provId"  @change="getChildren">
            <el-option :key="item[props.prop.value]" :label="item[props.prop.label]" :value="item[props.prop.value]" v-for="item in props.options"/>
        </el-select>
        <span class="label">城市</span>
        <el-select clearable placeholder="请选择市" v-model="cityId"  @change="cityChange">
            <el-option :key="item[props.prop.value]" :label="item[props.prop.label]" :value="item[props.prop.value]" v-for="item in cityOptions" />
        </el-select>
    </div>
</template>
<script setup>
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance()
const emits = defineEmits()
const props = defineProps({
    options:{
        type: Array,
        default: ()=> []
    },
    value:{
        type: [Array, String],
        required:true
    },
    prop:{
        type:Object,
        default: ()=>{ return {
            value: 'id',
            label: 'name'
        }}
    }
})
const provId = ref('')
const cityId = ref('')
const cityOptions = ref([])

watch(()=>props.value, val => {
    if(val == '' || (Array.isArray(val) && val.length == 0)){
        provId.value = ''
        cityId.value = ''
    }
})

function getChildren(data){
    const obj = proxy.findArrValue(props.options, 'id', data)
    // console.log(obj)
    if(obj?.children?.length>0){
        cityOptions.value = proxy.deepClone(obj.children)
    }
    else{
        cityOptions.value.length = 0
    }
    cityId.value = ''
    emits('update:value',data)
}
function cityChange(data) {
    emits('update:value',data)
}
</script>


<style lang="scss" scoped>
.addressSelectBox {
    display: flex;

    .label {
        color: #999999;
        font-weight: 400;
        font-size: 14px;
        margin: 0 10px;
    }
    :deep(.el-select){
        max-width: 140px;
    }
}
</style>