<template>
    <div class="avatarBox">
        <el-avatar  :size="props.size" :src="avatar" fit="fill" @error="()=>true">
            <img :src="props.sex === '0'?avatar1:avatar2"/>
        </el-avatar>
    </div>
</template>



<script setup>
import avatar1 from '@/assets/images/avatar1.jpg'
import avatar2 from '@/assets/images/avatar2.jpg'
import { isExternal } from '@/utils/validate'
const { proxy } = getCurrentInstance()
const props = defineProps({
    sex:{
        type:String||Number,
        default:'0'
    },
    url:{
        type:String,
        default:''
    },
    size:{
        type:Number
    }
})

const avatar = computed(() => {
    if(!props.url) return
    if (isExternal(props.url)){
        
        return props.url
    }
    else{
        console.log(import.meta.env.VITE_IMAGE + props.url, 'inm')
        return import.meta.env.VITE_IMAGE + props.url
    }
})


</script>

<style lang="scss" scoped>
.avatarBox{
    width: 100%;
    height:100%;
    display: flex;
    align-items: center;
    justify-content: center;
}    
</style>