<template>
   <div class="labelBox">
        <div class="labelRow">
            <el-tag
                v-for="tag in dynamicTags"
                :key="tag.id"
                class="label"
                effect="plain"
                closable
                size="large"
                :disable-transitions="false"
                @close="closeLabel(tag)"
            >
                {{ tag.name }}
            </el-tag>

        </div>

        <div class="handleRow">
            <el-button size="small" @click="addLabel" class="btn">
                + 添加标签
            </el-button>
            <span>最多可以添加5个标签</span>
        </div>
   </div>
</template>

<script setup name="label">

const dynamicTags = ref([
    { name:'标签1', id:1 },
    { name:'标签2', id:2 },
    { name:'标签3', id:3 },
    { name:'标签4', id:4 },
])

const addLabel = ()=> {

}

const closeLabel = (tag) => {
    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
} 

</script>


<style lang="scss" scoped>
.labelBox{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .labelRow{
        display: flex;
        .label{
            margin-right: 10px;
            margin-bottom: 10px;
        }
    }
    .handleRow{
        display: flex;
        align-items: flex-start;
        .btn{
            margin-right: 4px;
        }
        span{
            font-size: 12px;
            line-height: 24px;
        }
    }
}
    
</style>