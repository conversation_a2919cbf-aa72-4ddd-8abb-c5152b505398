<template>
  <div class="CoverViewBox">
    <el-image
      v-if="form[option[0]]"
      :src="baseUrl + form[option[0]]"
      fit="fill"
      class="pic"
      :preview-src-list="perviewList"
      :initial-index="0"
      preview-teleported
    >
    </el-image>
    <el-image
      :src="img"
      fit="fill"
      class="pic"
      v-for="(img, index) in imgList"
      :key="index"
      :preview-src-list="perviewList"
      :initial-index="index+1"
      preview-teleported
    ></el-image>
  </div>
</template>

<script setup name="CoverView">
import { computed } from "vue";

const { form, option } = defineProps({
  form: {
    type: Object,
    default: {},
  },
  option: {
    type: Array,
    default: ["coverImage", "images"],
  },
});

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const imgList = computed(() => {
  if (typeof form[option[1]] === "string" && form[option[1]] !== "") {
    return form[option[1]].split(",").map((i) => baseUrl + i);
  } else {
    return [];
  }
});
const perviewList = computed(() => {
  return [baseUrl + form[option[0]], ...imgList.value];
});
</script>

<style lang="scss" scoped>
.CoverViewBox {
  display: flex;
  flex-wrap: wrap;
  .pic {
    width: 150px;
    height: 150px;
    margin-right: 10px;
    margin-bottom: 10px;
    &:first-child::after {
      content: "封面";
      position: absolute;
      z-index: 20;
      top: 8px;
      right: 8px;
      background: var(--el-color-primary);
      color: #fff;
      padding: 0 6px;
      line-height: 20px;
      border-radius: 2px;
    }
  }
}
</style>
