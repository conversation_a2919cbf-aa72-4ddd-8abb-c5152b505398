<template>
    <DateRange v-model:value="val"
                        value-format="YYYY-MM-DD" />
</template>

<script setup name="doubleValsDate">
import { getCurrentInstance } from 'vue';


const { proxy } = getCurrentInstance()

const emits = defineEmits()
const props = defineProps({
    startVal:{
        type:String,
    },
    endVal:{
        type:String
    },
})

const val = ref(['',''])
watch(()=>props.startVal, startVal=> {
    if(startVal !== val.value[0]){
        val.value[0] = startVal
    }
},{immediate:true} )

watch(()=>props.endVal, endVal=> {
    if(endVal !== val.value[1]){
        val.value[1] = endVal
    }
},{immediate:true})

watch(()=>val.value, val=> {
    const startVal = val[0]
    const endVal = val[1]
    if(startVal !== props.startVal){
        emits('update:startVal',startVal)
    }
    if(endVal !== props.endVal){
        emits('update:endVal',endVal)
    }
} )
</script>