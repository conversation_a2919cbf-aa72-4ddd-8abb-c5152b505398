<template>
  <el-form :model="form" :rules="rules" label-width="7em" ref="formRef" class="commonForm">
    <el-row>
      <el-col :span="formItemSize(item.size)" v-for="(item, index) in visibleFormOptions" :key="index">
        <el-form-item :label="item.label" :prop="item.prop" :rules="item.rules" v-if="!props.readonly" :class="{ positonTop: item.position === 'top' }">
          <el-input v-if="item.type === 'String'" v-model="form[item.prop]" :placeholder="'请输入' + item.label" :type="item.option.type" :rows="item.option.rows || 4" />

          <Password v-if="item.type === 'Password'" v-model:value="form[item.prop]" :id="form.merchantUserId || form.id" :req="item.option.req" :label="item.label"></Password>

          <el-input-number v-if="item.type === 'Number'" v-model="form[item.prop]" :min="1" label="描述文字" :controls="false" :placeholder="`${item.label}`" style="text-align: left"></el-input-number>

          <ImageUpload v-if="item.type === 'Image'" v-model:modelValue="form[item.prop]" :hasCover="item.option.hasCover" :limit="item.option.limit" :isShowTip="false" />

          <div v-if="item.type === 'DoubleImage'" class="doubleImage">
            <ImageUpload v-model:modelValue="form[item.option.vals[0]]" :limit="1" :isShowTip="false" />
            <ImageUpload v-model:modelValue="form[item.option.vals[1]]" :limit="1" :isShowTip="false" />
          </div>

          <CoverImageUpload v-if="item.type === 'Cover'" v-model:cover="form[item.option.vals[0]]" v-model:others="form[item.option.vals[1]]" />

          <DoubleValsDate v-if="item.type === 'DateRange'" v-model:startVal="form[item.option.vals[0]]" v-model:endVal="form[item.option.vals[1]]" value-format="YYYY-MM-DD" />

          <el-radio-group v-if="item.type === 'Radio'" v-model="form[item.prop]" @change="" :disabled="item.option.addOnly ? props.readonly : false">
            <el-radio v-for="p in item.option.pull" :key="p.id || p.value" :label="Number(p.id || p.value)">
              {{ p.name || p.label }}
            </el-radio>
          </el-radio-group>

          <AddressSelect v-if="item.type === 'Address'" v-model:prov="form[item.option.vals[0]]" v-model:city="form[item.option.vals[1]]" v-model:area="form[item.option.vals[2]]" />

          <editor v-if="item.type === 'Editor'" v-model="form[item.prop]" :min-height="192" />

          <el-select v-if="item.type === 'Select'" v-model="form[item.prop]" :placeholder="`请选择${item.label}`" clearable filterable @change="">
            <el-option v-for="pullItem in item.option.pull" :key="pullItem.value" :label="pullItem.label" :value="pullItem.value"> </el-option>
          </el-select>

          <el-date-picker v-if="item.type === 'Date'" v-model="form[item.prop]" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" :placeholder="`请选择${item.label}`">
          </el-date-picker>

          <div v-if="item.type === 'Check'" class="checkBox">
            <el-checkbox v-model="form[item.prop]" :true-label="1" :false-label="0" @change="">{{ item.option.label }}</el-checkbox>
            <div class="tip">
              {{ item.option.tip }}
            </div>
          </div>

          <div v-if="item.type === 'Checks'" class="checkBox">
            <el-checkbox-group v-model="form[item.prop]" @change="">
              <el-checkbox v-for="pullItem in item.option.pull" :key="pullItem.key" :label="pullItem.value || pullItem.id">
                {{ pullItem.label || pullItem.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <div v-if="item.type === 'Read'">{{ item.option.vals }}</div>

          <Label v-if="item.type === 'Label'" />

          <slot v-if="item.type === 'Slot'" :name="item.prop" :data="{ form, config: item }"></slot>
        </el-form-item>

        <el-form-item :label="item.label" :prop="item.prop" v-else :class="{ positonTop: item.position === 'top' }">
          <div v-if="item.type === 'Read'">
            {{ item.option.vals }}
          </div>

          <el-image v-else-if="item.type === 'Image'" :src="baseUrl + form[item.prop]" fit="fill" class="pic"></el-image>

          <div v-else-if="item.type === 'Cover'">
            <CoverView :option="item.option.vals" :form="form" />
            <!-- <el-image :src="form[item.option.vals[1]]" fit="fill"></el-image> -->
          </div>

          <div v-else-if="item.type === 'Select' || item.type === 'Radio'">
            {{ getValue(form[item.prop], item.option.pull) }}
          </div>

          <div v-else-if="item.type === 'Checks'">
            {{ getValues(form[item.prop], item.option.pull) }}
          </div>

          <div v-else-if="item.type === 'Editor'">
            {{ htmlToStr(form[item.prop]) }}
          </div>

          <slot v-else-if="item.type === 'Slot'" :name="item.prop" :data="{ form, config: item }"></slot>

          <div v-else-if="item.option.type === 'textarea'">
            <div v-html="form[item.prop]"></div>
            <!-- <div>111</div> -->
          </div>

          <span v-else style="word-wrap: break-word; width: 100%">{{ form[item.prop] || '无' }}</span>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import CoverImageUpload from './coverImageUpload.vue'
import DoubleValsDate from './doubleValsDate.vue'
import Label from './label.vue'
import CoverView from './CoverView.vue'
import Password from './password.vue'
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
  formOptions: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
})
const baseUrl = import.meta.env.VITE_APP_BASE_API
const rules = ref({})
const form = ref({})
const formRef = ref(null)

// 计算属性：过滤出可见的表单项（排除隐藏字段）
const visibleFormOptions = computed(() => {
  return props.formOptions.filter(item => item.type !== 'Hidden')
})

// 处理隐藏字段的初始化
const initHiddenFields = () => {
  props.formOptions.forEach(item => {
    if (item.type === 'Hidden' && item.defaultValue !== undefined) {
      form.value[item.prop] = item.defaultValue
    }
  })
}

watch(
  () => form.value,
  (val) => {
    Object.keys(val).map((key) => {
      if (val[key] !== props.form[key]) props.form[key] = val[key]
    })
  },
  { deep: true }
)

watch(
  () => props.form,
  (val) => {
    Object.keys(val).map((key) => {
      if (val[key] !== form.value[key]) form.value[key] = val[key]
    })
  },
  { immediate: true, deep: true }
)

// 监听 formOptions 变化，初始化隐藏字段
watch(
  () => props.formOptions,
  () => {
    initHiddenFields()
  },
  { immediate: true, deep: true }
)

defineExpose({
  formRef,
})

function getValue(val, options) {
  if (Array.isArray(options) && options.length > 0) {
    const afterFliter = options.filter((i) => i.value === val)
    if (afterFliter.length > 0) {
      return afterFliter[0].label
    }
    return val
  }
  return val
}

function getValues(val, pull) {
  if (val.length == 2) {
    return '商家端、用户端'
  } else if (val == 2) {
    return '用户端'
  } else if (val == 1) {
    return '商家端'
  }
}
</script>

<style lang="scss" scoped>
.commonForm {
  :deep(.el-input-number) {
    width: 100%;
    .el-input__inner {
      text-align: left;
      width: 100%;
    }
  }

  .tip {
    margin-left: 4em;
    line-height: normal;
    color: rgba(153, 153, 153, 1);
  }
  .doubleImage {
    display: flex;
    .component-upload-image {
      margin-right: 10px;
    }
  }
  .positonTop {
    display: block;
    &:deep(.el-form-item__label) {
      width: fit-content !important;
    }
    &:deep(.el-form-item__content) {
      padding-left: 3em;
      padding-top: 8px;
    }
  }
}
</style>
