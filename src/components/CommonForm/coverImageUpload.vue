<template>
    <ImageUpload v-model:modelValue="value" :hasCover="true" :limit="props.limit" :isShowTip="false"/>
</template>


<script setup>
import { getCurrentInstance } from 'vue';

const {proxy} = getCurrentInstance()
const emits = defineEmits()
const props = defineProps({

    cover:{
        type: String
    },
    others:{
        type:String
    },
    limit:{
        type:Number,
        default:40
    }
})


const value = ref('')

watch(()=>value.value, val => {
    // console.log(val,'change')
    if(val && val !== ''){
        var newValArray = val.split(',')
        var othersArray = props.others.split(',')
        // console.log(newValArray,othersArray)
        var cover = props.cover
        if(newValArray.length > 0){
            newValArray.forEach((item,index) => {
                if(index > 0){
                    othersArray[index - 1] = item
                }
                else{
                    if(cover !== newValArray[0]){
                        cover = newValArray[0]
                    }
                    othersArray.length = 0
                }
            })
            console.log('=-=',othersArray)
            emits('update:cover', cover)
            emits('update:others', othersArray.join(','))
        }
    }
    else{
        emits('update:cover', '')
        emits('update:others', '')
    }
})

watch(()=>props.cover, val=> {
    if(val){
        const nowArray = value.value.split(',')
        const nowCover = nowArray[0]
        if(val !== nowCover){
            nowArray[0] = val
            value.value = nowArray.join(',')
        }
    }
    else{
        value.value = ''
    }
    
}, {immediate:true})

watch(()=>props.others, val=> {
    if(val){
        const propValue = props.cover + ',' + val
        if(propValue !== value.value) value.value = propValue
    }
    else{
        value.value = props.cover
    }
}, {immediate:true})

</script>