<template>
  <div class="passWordBox">
    <el-input
      class="input"
      v-model="val"
      :placeholder="'请输入' + props.label"
      type="password"
      @change="inputChange"
      :show-password="true"
      :disabled="disabled"
    />

    <btn-for-change-pw class="btn" border style="margin-left: 10px" :req="props.req" :rowId="props.id" v-if="props.req && props.id"/>
  </div>
</template>

<script setup name="password">
const emits = defineEmits();
const props = defineProps({
  value: {
    type: String,
  },
  req:{
    type:Function,
    default:undefined
  },
  id:{
    type:Number,
    default:undefined
  },
  label:{
    type: String,
    default:undefined
  }
});

const initpwd = ref("*******");
const disabled = ref(false);
const val = ref("");

function isEncoded(code) {
  if (typeof code === "string" && code.startsWith("$") && code.length > 30) {
    return true;
  } else {
    return false;
  }
}

function inputChange(data) {
  emits("update:value", data);
}
watch(
  () => props.value,
  (value) => {
    if (isEncoded(value)) {
      val.value = initpwd.value;
      // emits("update:value", 'shanghaiyouying2023');
      disabled.value = true;
    } else {
      disabled.value = false;
      if (value !== val.value) {
        val.value = value;
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.passWordBox {
  display: flex;
  width: 100%;
  .input {
    flex-grow: 1;
    margin-right: 10px;
  }
  .btn {
    // margin-left: 10px;
    flex-shrink: 0;
  }
}
</style>
