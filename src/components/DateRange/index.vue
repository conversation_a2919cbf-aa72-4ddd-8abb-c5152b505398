<template>

    <div class="dateRange">
        <el-date-picker
            class="picker leftPicker"
            v-model="startValue"
            type="date"
            :placeholder="props.startPlaceholder"
            :disabled-date="props.disabledDate"
            :size="props.size"
            :clearable="props.clearable"
            :editable="props.editable"
            :value-format="props.valueFormat"
            @change="startValChange"
        />
        <span class="separator">至</span>
        <el-date-picker
            class="picker rightPicker"
            v-model="endValue"
            type="date"
            :placeholder="props.endPlaceholder"
            :disabled-date="disabledDate"
            :size="props.size"
            :clearable="props.clearable"
            :editable="props.editable"
            :value-format="props.valueFormat"
            @change="endValChange"
        />
    </div>
</template>


<script setup name="DateRange">
import moment from 'moment'
const props = defineProps({
    // 绑定值
    value:{
        type:Array
    },
    // 左输入框占位符
    startPlaceholder:{
        type:String,
        default:'开始日期'
    },
    // 右输入框占位符
    endPlaceholder:{
        type:String,
        default:'结束日期'
    },
    // 大小
    size:{
        type:String,
        default:'default'
    },
    // 日期格式
    valueFormat:{
        type:String,
        default:'YYYY-MM-DD'
    },
    // 日期禁用（只作用于开始日期）
    disabledDate:{
        type:Function,
        default:() => false
    },
    clearable:{
        type:Boolean,
        default: true
    },
    editable:{
        type:Boolean,
        default: true
    }
})



// 开始值和结束值的定义与赋值
const startValue = ref('')
const endValue = ref('')
const reset = () => {
    startValue.value = ''
    endValue.value = ''
}
watch(()=>props.value, val => {
    if(Array.isArray(val) && val.length>0){
        startValue.value = val[0]
        endValue.value = val[1]
    }
    else{
        reset()
    }
},{immediate:true, deep:true})

// 更新绑定值
const emits = defineEmits()
const updateVal = () => {
    emits('update:value', [startValue.value, endValue.value])
    if((startValue.value && endValue.value) || !(startValue.value || endValue.value) ){
        emits('change')
    }
}
const startValChange = (data) => {
    if(data){
        if(moment(data).isAfter(moment(endValue.value))){
            endValue.value = ''
        }
    }
    else{
        endValue.value = ''
    }
    
    updateVal()
}
const endValChange = () => {
    updateVal()
}

// 判断end选择器日期的禁用
const disabledDate = (date) => {
    if(startValue.value){
        return moment(date).isBefore(moment(startValue.value)) || props.disabledDate(date)
    }
    else{
        return true
    }
    
}

</script>


<style lang="scss" scoped>
.dateRange{
    // width: 100%;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    :deep(.picker){
        width:150px;
        flex-grow: 1;
    }
    .separator{
        margin: 0 10px;
    }

}
</style>